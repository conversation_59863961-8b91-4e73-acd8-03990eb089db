import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';
import 'package:choice_once_upon_a_time/models/story_metadata_model.dart';

/// Service for accessing Firestore stories collection
/// 
/// Handles the stories collection with structure:
/// - story_id (document ID)
/// - title (String)
/// - description (String) 
/// - zip_url (String) - URL to downloadable ZIP file
/// - cover_image_url (String) - URL to cover image
class FirestoreStoryService {
  static const String _logPrefix = '[FIRESTORE_STORY_SERVICE]';
  
  final FirebaseFirestore _firestore;
  
  FirestoreStoryService({FirebaseFirestore? firestore}) 
      : _firestore = firestore ?? FirebaseFirestore.instance;

  /// Get all stories from Firestore stories collection
  Future<List<StoryMetadataModel>> getAllStories() async {
    try {
      AppLogger.debug('$_logPrefix: Fetching all stories from Firestore');
      
      final querySnapshot = await _firestore.collection('stories').get();
      final stories = <StoryMetadataModel>[];
      
      AppLogger.debug('$_logPrefix: Found ${querySnapshot.docs.length} stories in Firestore');
      
      for (final doc in querySnapshot.docs) {
        final data = doc.data();
        final storyId = doc.id;
        
        AppLogger.debug('$_logPrefix: Processing story $storyId');
        
        // Create StoryMetadataModel from Firestore document
        final story = StoryMetadataModel(
          id: storyId,
          title: {'en-US': data['title']?.toString() ?? 'Untitled Story'},
          coverImageUrl: data['cover_image_url']?.toString() ?? 'assets/images/story_covers/default_cover.png',
          loglineShort: {'en-US': data['description']?.toString() ?? 'A wonderful story awaits!'},
          targetMoralValue: data['moral_value']?.toString() ?? 'friendship',
          targetAgeSubSegment: data['age_group']?.toString() ?? '5-7',
          estimatedDurationMinutes: (data['duration'] as num?)?.toInt() ?? 10,
          isFree: data['is_free'] as bool? ?? true,
          initialSceneId: '${storyId}_scene_01',
          dataSource: 'firebase',
          published: data['published'] as bool? ?? true,
        );
        
        stories.add(story);
        AppLogger.debug('$_logPrefix: Added story ${story.id} - ${story.title['en-US']}');
      }
      
      AppLogger.info('$_logPrefix: Successfully loaded ${stories.length} stories from Firestore');
      return stories;
      
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error fetching stories from Firestore', e, stackTrace);
      return [];
    }
  }

  /// Get a specific story by ID from Firestore
  Future<StoryMetadataModel?> getStoryById(String storyId) async {
    try {
      AppLogger.debug('$_logPrefix: Fetching story $storyId from Firestore');
      
      final doc = await _firestore.collection('stories').doc(storyId).get();
      
      if (!doc.exists) {
        AppLogger.debug('$_logPrefix: Story $storyId not found in Firestore');
        return null;
      }
      
      final data = doc.data()!;
      
      final story = StoryMetadataModel(
        id: storyId,
        title: {'en-US': data['title']?.toString() ?? 'Untitled Story'},
        coverImageUrl: data['cover_image_url']?.toString() ?? 'assets/images/story_covers/default_cover.png',
        loglineShort: {'en-US': data['description']?.toString() ?? 'A wonderful story awaits!'},
        targetMoralValue: data['moral_value']?.toString() ?? 'friendship',
        targetAgeSubSegment: data['age_group']?.toString() ?? '5-7',
        estimatedDurationMinutes: (data['duration'] as num?)?.toInt() ?? 10,
        isFree: data['is_free'] as bool? ?? true,
        initialSceneId: '${storyId}_scene_01',
        dataSource: 'firebase',
        published: data['published'] as bool? ?? true,
      );
      
      AppLogger.debug('$_logPrefix: Successfully loaded story ${story.id} - ${story.title['en-US']}');
      return story;
      
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error fetching story $storyId from Firestore', e, stackTrace);
      return null;
    }
  }

  /// Get the ZIP download URL for a story
  Future<String?> getStoryZipUrl(String storyId) async {
    try {
      AppLogger.debug('$_logPrefix: Getting ZIP URL for story $storyId');
      
      final doc = await _firestore.collection('stories').doc(storyId).get();
      
      if (!doc.exists) {
        AppLogger.debug('$_logPrefix: Story $storyId not found in Firestore');
        return null;
      }
      
      final data = doc.data()!;
      final zipUrl = data['zip_url']?.toString();
      
      AppLogger.debug('$_logPrefix: ZIP URL for story $storyId: $zipUrl');
      return zipUrl;
      
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error getting ZIP URL for story $storyId', e, stackTrace);
      return null;
    }
  }

  /// Check if a story exists in Firestore
  Future<bool> storyExists(String storyId) async {
    try {
      final doc = await _firestore.collection('stories').doc(storyId).get();
      return doc.exists;
    } catch (e) {
      AppLogger.error('$_logPrefix: Error checking if story $storyId exists', e);
      return false;
    }
  }

  /// Get story count from Firestore
  Future<int> getStoryCount() async {
    try {
      final querySnapshot = await _firestore.collection('stories').get();
      final count = querySnapshot.docs.length;
      AppLogger.debug('$_logPrefix: Story count in Firestore: $count');
      return count;
    } catch (e) {
      AppLogger.error('$_logPrefix: Error getting story count from Firestore', e);
      return 0;
    }
  }

  /// Stream stories for real-time updates
  Stream<List<StoryMetadataModel>> streamStories() {
    return _firestore.collection('stories').snapshots().map((snapshot) {
      final stories = <StoryMetadataModel>[];
      
      for (final doc in snapshot.docs) {
        final data = doc.data();
        final storyId = doc.id;
        
        final story = StoryMetadataModel(
          id: storyId,
          title: {'en-US': data['title']?.toString() ?? 'Untitled Story'},
          coverImageUrl: data['cover_image_url']?.toString() ?? 'assets/images/story_covers/default_cover.png',
          loglineShort: {'en-US': data['description']?.toString() ?? 'A wonderful story awaits!'},
          targetMoralValue: data['moral_value']?.toString() ?? 'friendship',
          targetAgeSubSegment: data['age_group']?.toString() ?? '5-7',
          estimatedDurationMinutes: (data['duration'] as num?)?.toInt() ?? 10,
          isFree: data['is_free'] as bool? ?? true,
          initialSceneId: '${storyId}_scene_01',
          dataSource: 'firebase',
          published: data['published'] as bool? ?? true,
        );
        
        stories.add(story);
      }
      
      AppLogger.debug('$_logPrefix: Streamed ${stories.length} stories from Firestore');
      return stories;
    });
  }
}
