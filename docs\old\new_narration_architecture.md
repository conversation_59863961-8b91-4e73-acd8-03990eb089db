# New Story Narration System Architecture

## Overview
This document outlines the architecture for the completely redesigned story narration system, following clean architecture principles with proper separation of concerns.

## Architecture Layers

### 1. Domain Layer (Core Business Logic)
- **Models**: Pure data models representing narration concepts
- **Interfaces**: Abstract contracts for services
- **Entities**: Core business entities for narration state

### 2. Data Layer (External Dependencies)
- **TTS Service Implementation**: Concrete TTS service using flutter_tts
- **Settings Repository**: Narration settings persistence
- **Progress Repository**: Narration progress tracking

### 3. Presentation Layer (UI Components)
- **Widgets**: Reusable narration UI components
- **Providers**: Riverpod state management
- **Controllers**: Widget-specific logic

## Core Components

### Domain Models

#### NarrationState
```dart
enum NarrationStatus { idle, loading, playing, paused, completed, error }

class NarrationState {
  final NarrationStatus status;
  final String? currentText;
  final int currentWordIndex;
  final double progress;
  final String? error;
  final Duration? duration;
}
```

#### NarrationConfig
```dart
class NarrationConfig {
  final double speechRate;
  final double speechPitch;
  final double speechVolume;
  final String language;
  final bool autoProgress;
  final bool highlightWords;
  final String emotionCue;
}
```

#### WordHighlight
```dart
class WordHighlight {
  final int startIndex;
  final int endIndex;
  final String word;
  final Duration startTime;
  final Duration endTime;
  final bool isActive;
}
```

### Service Interfaces

#### INarrationService
```dart
abstract class INarrationService {
  Stream<NarrationState> get stateStream;
  Future<void> initialize();
  Future<void> narrateText(String text, {NarrationConfig? config});
  Future<void> narrateScene(EnhancedSceneModel scene);
  Future<void> play();
  Future<void> pause();
  Future<void> stop();
  Future<void> seekToWord(int wordIndex);
  void dispose();
}
```

#### ITTSService
```dart
abstract class ITTSService {
  Stream<TTSEvent> get eventStream;
  Future<bool> initialize();
  Future<void> speak(String text, {TTSParameters? parameters});
  Future<void> pause();
  Future<void> resume();
  Future<void> stop();
  Future<List<TTSVoice>> getAvailableVoices();
  Future<void> setVoice(TTSVoice voice);
}
```

### Implementation Services

#### StoryNarrationService
- Implements INarrationService
- Manages scene-by-scene narration flow
- Handles word-level highlighting synchronization
- Integrates with TTS service for speech synthesis
- Provides progress tracking and state management

#### EnhancedTTSService
- Implements ITTSService
- Wraps flutter_tts with enhanced error handling
- Supports emotion-based voice modulation
- Provides detailed event streaming
- Handles voice selection and parameters

#### WordHighlightService
- Manages word-level highlighting logic
- Synchronizes highlighting with TTS events
- Calculates word timing and positioning
- Provides smooth highlighting animations

### Presentation Components

#### NarrationWidget
- Main narration display widget
- Shows text with word-level highlighting
- Displays narration progress
- Handles user interactions

#### NarrationControlsWidget
- Play/pause/stop controls
- Progress slider
- Speed adjustment
- Replay functionality

#### WordHighlightWidget
- Individual word highlighting component
- Smooth animation transitions
- Configurable highlight styles
- Responsive design

## Key Features

### 1. Word-Level Highlighting
- Real-time synchronization with TTS
- Smooth animation transitions
- Configurable highlight colors and styles
- Support for different text layouts

### 2. Enhanced Error Handling
- Graceful degradation on TTS failures
- Retry mechanisms with exponential backoff
- User-friendly error messages
- Fallback to text-only mode

### 3. Emotion-Based Voice Modulation
- Dynamic voice parameter adjustment
- Emotion cue mapping to speech characteristics
- Smooth transitions between emotions
- Character-specific voice profiles

### 4. Progress Management
- Sentence and word-level progress tracking
- Resume from last position
- Progress persistence across sessions
- Visual progress indicators

### 5. Responsive Design
- Adaptive layouts for different screen sizes
- Orientation-aware components
- Accessibility support
- Theme consistency

## Integration Points

### Enhanced Story System
- Compatible with EnhancedStoryModel
- Supports character profiles and narrator settings
- Integrates with scene progression
- Handles choice-based branching

### Voice Guide System
- Coordinates with existing voice guidance
- Prevents audio conflicts
- Shared TTS resource management
- Priority-based audio scheduling

### Settings Service
- Persistent narration preferences
- User customization options
- Accessibility settings integration
- Real-time settings updates

## Testing Strategy

### Unit Tests
- Service interface implementations
- Domain model validation
- Business logic verification
- Error handling scenarios

### Widget Tests
- UI component rendering
- User interaction handling
- Responsive design validation
- Animation testing

### Integration Tests
- End-to-end narration flow
- TTS service integration
- Story system compatibility
- Performance validation

## Performance Considerations

### Memory Management
- Efficient text processing
- Word highlighting optimization
- Proper resource disposal
- Memory leak prevention

### Audio Performance
- TTS service optimization
- Audio buffer management
- Smooth playback transitions
- Low-latency controls

### UI Performance
- Smooth highlighting animations
- Efficient widget rebuilds
- Responsive user interactions
- 60fps target maintenance

## Future Enhancements

### Advanced Features
- Multi-language support
- Voice cloning integration
- Background music synchronization
- Advanced emotion recognition

### Accessibility
- Screen reader integration
- Voice control support
- High contrast modes
- Customizable text sizes

### Analytics
- Narration engagement tracking
- Performance metrics
- User preference analysis
- Error reporting
