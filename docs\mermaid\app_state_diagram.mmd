graph TD
    %% Entry Point
    A[main.dart] --> B[WidgetsFlutterBinding.ensureInitialized]
    A --> C[dotenv.load]
    A --> D[Firebase.initializeApp]
    A --> E[AccessibilityService.initialize]
    A --> F[StoryRewardsService.initialize]
    A --> G[ProviderScope]
    
    %% Core App Widget
    G --> H[AppWidget]
    H --> I[AppRouter.router]
    H --> J[AppTheme.lightTheme]
    H --> K[GlobalNarratorWidget]
    H --> L[PopScope - Back Button Handler]
    
    %% Router Configuration
    I --> M[GoRouter Routes]
    
    %% App Initialization Flow
    M --> N[/launch - LaunchScreen]
    N --> O[/ftue - FTUEScreen]
    O --> P[/welcome - WelcomeScreen]
    P --> Q[/profile_selection - ProfileSelectionScreen]
    Q --> R[/home - HomeScreen]
    
    %% Main App Flow
    R --> S[/story_library - StoryLibraryScreen]
    S --> T[Story Selection]
    T --> U[/new_story/introduction/:storyId]
    U --> V[/new_story/character_introduction/:storyId]
    V --> W[/new_story/play/:storyId - NewStoryPlayerScreen]
    
    %% Story Player States
    W --> X[Scene Loading]
    X --> Y[Image Display + 500ms Wait]
    Y --> Z[Sentence-by-Sentence Narration]
    Z --> AA[Word Highlighting During TTS]
    AA --> BB[1000ms Pause Between Sentences]
    BB --> CC{Last Sentence?}
    CC -->|No| Z
    CC -->|Yes| DD{Choices Available?}
    DD -->|Yes| EE[Show Choice Popup]
    DD -->|No| FF{Auto/Manual Mode?}
    FF -->|Auto| GG[Auto Advance to Next Scene]
    FF -->|Manual| HH[Wait for User Input]
    EE --> II[Narrate Choice Options]
    II --> JJ[User Selects Choice]
    JJ --> KK[Load Next Scene Based on Choice]
    HH --> LL[User Taps Next]
    LL --> GG
    GG --> X
    KK --> X
    
    %% Parent Zone Flow
    R --> MM[/parent_gate_entry - ParentalGateScreen]
    MM --> NN[/parent_gate_entry/auth - ParentAuthScreen]
    NN --> OO[/parent_zone - ParentZoneDashboardScreen]
    OO --> PP[/parent_zone/sound_settings]
    OO --> QQ[/parent_zone/user_profiles]
    OO --> RR[/parent_zone/progress_tracking]
    OO --> SS[/parent_zone/about_stories]
    OO --> TT[/parent_zone/manage_downloads]
    OO --> UU[/parent_zone/help_support]
    OO --> VV[/parent_zone/narrator_creation]
    
    %% Core Services Integration
    E --> WW[AccessibilityService]
    WW --> XX[High Contrast Mode]
    WW --> YY[Large Text Mode]
    WW --> ZZ[Voice Over Support]
    WW --> AAA[Reduced Motion]
    
    F --> BBB[StoryRewardsService]
    BBB --> CCC[Achievement Tracking]
    BBB --> DDD[Progress Rewards]
    BBB --> EEE[Story Completion Badges]
    
    %% Global Narrator System
    K --> FFF[GlobalNarratorController]
    FFF --> GGG[Route-Based Narration]
    GGG --> HHH[Screen Introduction Audio]
    HHH --> III[TTS Integration]
    
    %% Theme System
    J --> JJJ[Color Scheme]
    J --> KKK[Typography]
    J --> LLL[Component Themes]
    JJJ --> MMM[Primary: Soft Peach]
    JJJ --> NNN[Secondary: Dusty Teal]
    JJJ --> OOO[Accent: Coral Pink]
    
    %% State Management
    G --> PPP[Riverpod Providers]
    PPP --> QQQ[StoryLibraryProvider]
    PPP --> RRR[UserProfileProvider]
    PPP --> SSS[StoryProgressProvider]
    PPP --> TTT[GlobalNarratorProvider]
    
    %% Story Player Controls
    W --> UUU[Control Interface]
    UUU --> VVV[Previous Sentence/Scene Button]
    UUU --> WWW[Play/Pause Narration Button]
    UUU --> XXX[Next Sentence/Scene Button]
    UUU --> YYY[Settings Button]
    UUU --> ZZZ[Exit Button]
    
    %% Settings Integration
    YYY --> AAAA[StorySettingsWidget]
    AAAA --> BBBB[Font Size Control]
    AAAA --> CCCC[Narration Speed Control]
    AAAA --> DDDD[Subtitle Transparency]
    AAAA --> EEEE[Auto/Manual Mode Toggle]
    
    %% Error Handling
    W --> FFFF{Loading Error?}
    FFFF -->|Yes| GGGG[Error Screen with Retry]
    FFFF -->|No| X
    GGGG --> W
    
    %% Exit Flow
    ZZZ --> HHHH[/calm_exit - CalmExitScreen]
    HHHH --> R
    
    %% Additional Features
    R --> IIII[/rewards - RewardsScreen]
    R --> JJJJ[/continue_story - ContinueStoryScreen]
    R --> KKKK[/ai_story_generation - AIStoryGenerationScreen]
    
    %% Testing Routes
    M --> LLLL[/fallback_asset_test - FallbackAssetTestScreen]
    M --> MMMM[/timing_test/:storyId - TimingTestScreen]
    
    %% Story Data Flow
    S --> NNNN[StoryRepository]
    NNNN --> OOOO[AssetOnlyStoryService]
    NNNN --> PPPP[EnhancedStoryService]
    NNNN --> QQQQ[StoryScannerService]
    NNNN --> RRRR[FirebaseStoryService]
    
    %% Narration System
    Z --> SSSS[EnhancedStoryNarrationService]
    SSSS --> TTTT[TTS Engine]
    SSSS --> UUUU[Word-Level Highlighting]
    SSSS --> VVVV[Emotion Cue Processing]
    SSSS --> WWWW[Speech Rate Control]
    
    %% Progress Tracking
    W --> XXXX[StoryProgressService]
    XXXX --> YYYY[Scene Progress]
    XXXX --> ZZZZ[Choice Tracking]
    XXXX --> AAAAA[Time Tracking]
    XXXX --> BBBBB[Completion Status]
    
    %% Profile Integration
    Q --> CCCCC[UserProfileService]
    CCCCC --> DDDDD[Child Profile Creation]
    CCCCC --> EEEEE[Story Mode Preferences]
    CCCCC --> FFFFF[Progress Association]
    CCCCC --> GGGGG[Avatar & Settings]
    
    %% Style Definitions
    classDef entryPoint fill:#ff9999,stroke:#333,stroke-width:3px
    classDef coreService fill:#99ccff,stroke:#333,stroke-width:2px
    classDef screen fill:#99ff99,stroke:#333,stroke-width:2px
    classDef storyFlow fill:#ffcc99,stroke:#333,stroke-width:2px
    classDef decision fill:#ff99ff,stroke:#333,stroke-width:2px
    classDef dataLayer fill:#e6ccff,stroke:#333,stroke-width:2px
    classDef uiComponent fill:#ccffcc,stroke:#333,stroke-width:2px

    class A entryPoint
    class E,F,WW,BBB,FFF,SSSS,XXXX,CCCCC coreService
    class N,O,P,Q,R,S,W,MM,NN,OO screen
    class X,Y,Z,AA,BB,GG,KK storyFlow
    class CC,DD,FF,FFFF decision
    class NNNN,OOOO,PPPP,QQQQ,RRRR dataLayer
    class UUU,VVV,WWW,XXX,YYY,ZZZ,AAAA uiComponent
