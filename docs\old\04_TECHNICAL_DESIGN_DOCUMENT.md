# Detailed Technical Design Document (TDD)
## Project: Choice: Once Upon A Time
**Version:** 1.0
**Date:** May 30, 2025

## 1. Introduction & Overview

### 1.1. Project Purpose & Goals
"Choice: Once Upon A Time" is an interactive bedtime story Flutter app for children aged 4-7 years[cite: 2]. Its primary goal is to create engaging and delightful narrative experiences that subtly instill and reinforce positive moral values (honesty, empathy, perseverance, etc.), fostering social and emotional development[cite: 3]. The app aims to provide a calming bedtime routine enhancement through choice-driven stories brought to life by a unique, empathetic AI narrator, encouraging active participation and thoughtful decision-making in a safe, ad-free environment[cite: 4].

### 1.2. Scope of this TDD
This TDD covers the technical design for the Minimum Viable Product (MVP) of the "Choice: Once Upon A Time" application[cite: 5]. The MVP will include:
* Core Flutter application for iOS and Android[cite: 6].
* An initial set of 3 interactive moral stories: "Pip and the Pantry Puzzle" (Honesty), "Lila Fox and the Moonpetal Wish" (Empathy/Kindness), and "<PERSON><PERSON>'s Fantastic Flying Machine" (Perseverance), complete with branching narratives[cite: 7].
* Implementation of an empathetic AI narrator using on-device Text-to-Speech (TTS) capabilities with emotional modulation[cite: 8].
* User interface and experience as defined in the finalized UI/UX design specifications (Task 2.2 update)[cite: 9].
* Asset management for visuals and audio as per the Art Style Definition (Task 2.3) and Sound Design Concept (Task 2.4)[cite: 10].
* Offline access to downloaded stories[cite: 11].
* Parent Zone with basic settings (sound controls) and a Parental Gate[cite: 11].
* Monetization via a freemium model with a subscription to unlock all content (Task 1.8)[cite: 12].

### 1.3. Target Audience
(Summarized from Task 1.3)
* **Children (Ages 4-7):** Primary users. Developing cognitive/language skills, 10-20 min attention span[cite: 13]. Understand simple narratives, cause-effect, and moral concepts via concrete examples. Proficient with basic touch-screen interactions (taps, simple swipes)[cite: 14]. Enjoy animals, fantasy, and adventure. Need clear cues, immediate feedback, and a calming experience. Represented by persona "Lily, 5"[cite: 15].
* **Parents/Guardians:** Purchasers and facilitators. Seek moral reinforcement, educational value, quality entertainment, calming bedtime routine, and safe screen time[cite: 16]. Concerns include excessive screen time, addictive elements, and age-inappropriateness. Value transparency, ethical monetization, and features like offline access and parental controls[cite: 17]. Represented by persona "Sarah"[cite: 18].

### 1.4. Key Assumptions & Dependencies
* **On-Device TTS Feasibility:** The primary strategy of using native on-device TTS (via `flutter_tts` with SSML/cues) can achieve an acceptable level of emotional narration for the MVP[cite: 18]. Extensive R&D for a custom TTS engine is out of MVP scope[cite: 19].
* **Flutter Development:** Flutter (latest stable version at project start) is the confirmed frontend technology for cross-platform (iOS & Android) development[cite: 20].
* **Firebase BaaS:** Firebase will be used for backend services (Firestore, Cloud Storage, Authentication, Cloud Functions), reducing custom backend development time[cite: 21].
* **MVP Scope Adherence:** The project will strictly adhere to the defined MVP scope (3 stories, core features) to manage timeline and budget (Task 1.9)[cite: 22].
* **Content Availability:** Story scripts (Story 01, 02, 03), art assets (Task 2.3), and sound design elements (Task 2.4) will be available as per the project timeline[cite: 23].
* **Team Skills:** The team structure (Task 1.7) possesses the necessary skills for Flutter development, UI/UX design, content creation, and Firebase integration, with specialized AI/TTS support available[cite: 24].
* **Monetization Approval:** The freemium/subscription model (Task 1.8) will be compliant with app store policies[cite: 25].
* **UI/UX Design:** The "Task 2.2 update" document is the definitive source for screen specifications and user flows[cite: 26].
* **Asset Design:** "Task 2.3" (Art Style) and "Task 2.4" (Sound Design) are the definitive sources for asset requirements[cite: 27].

---
## 2. System Architecture

### 2.1. High-Level Architecture Diagram [cite: 28]

```mermaid
graph TD
    User[Child/Parent User] -->|Interacts with| FlutterApp[Flutter Mobile App (iOS & Android)];
    subgraph FlutterApp [cite: 29]
        direction LR
        UI[UI Layer (Widgets, Screens - Task 2.2)]
        StateMgmt[State Management (Riverpod - Task 1.6)]
        StoryEngine[Story Logic & Branching Engine]
        NarratorTTS[Empathetic Narrator (On-Device TTS - flutter_tts, SSML - Task 1.6)]
        OfflineStorage[Offline Storage (IsarDB, Assets - Task 1.6)]
        IAP[In-App Purchases (App Stores)] [cite: 30]
    end
    FlutterApp -->|API Calls/SDKs| FirebaseBaaS[Firebase BaaS (Task 1.6)]; [cite: 31]
    FlutterApp -->|Read/Write Local Data| DeviceStorage[Device Local Storage (Filesystem)];
    FlutterApp -->|TTS Requests (On-Device)| NativeTTS[Native OS TTS Engines (iOS/Android)]; [cite: 32]
    subgraph FirebaseBaaS
        direction TB
        Auth[Firebase Authentication]
        Firestore[Cloud Firestore (Story Data, User Data - Task 1.6)]
        Storage[Cloud Storage (Images, Audio Assets - Task 1.6)]
        Functions[Cloud Functions (Subscription Validation, etc. - Task 1.6)]
    end
    ContentCreators[Content Creators (Writers, Artists, Sound Designers)] -->|Upload Assets & Story Data| AdminTools[Admin Tools/Scripts (Future - for Content Management)]; [cite: 33]
    AdminTools --> FirebaseBaaS;
    FlutterApp -->|IAP Transactions| AppStoreServices[App Store Services (Apple App Store, Google Play Store)]; [cite: 34]

    style FlutterApp fill:#D6EAF8,stroke:#2980B9,stroke-width:2px
    style FirebaseBaaS fill:#D5F5E3,stroke:#27AE60,stroke-width:2px
    style DeviceStorage fill:#FCF3CF,stroke:#F39C12,stroke-width:1px
    style NativeTTS fill:#FCF3CF,stroke:#F39C12,stroke-width:1px
    style AppStoreServices fill:#FDEDEC,stroke:#C0392B,stroke-width:1px
    style AdminTools fill:#EBDEF0,stroke:#8E44AD,stroke-width:1px
    style User fill:#E8DAEF,stroke:#8E44AD,stroke-width:2px
    style ContentCreators fill:#E8DAEF,stroke:#8E44AD,stroke-width:2px