import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'narration_models.g.dart';

/// Enumeration for narration status states
enum NarrationStatus {
  @JsonValue('idle')
  idle,
  @JsonValue('loading')
  loading,
  @JsonValue('playing')
  playing,
  @JsonValue('paused')
  paused,
  @JsonValue('completed')
  completed,
  @JsonValue('error')
  error,
}

/// Represents the current state of narration playback
@JsonSerializable()
class NarrationState extends Equatable {
  /// Current status of the narration
  final NarrationStatus status;
  
  /// The text currently being narrated
  final String? currentText;
  
  /// Index of the currently highlighted word
  final int currentWordIndex;
  
  /// Progress as a percentage (0.0 to 1.0)
  final double progress;
  
  /// Error message if status is error
  final String? error;
  
  /// Total duration of the narration
  final int? durationMs;
  
  /// Current position in milliseconds
  final int? positionMs;
  
  /// Whether auto-progression is enabled
  final bool autoProgress;

  const NarrationState({
    this.status = NarrationStatus.idle,
    this.currentText,
    this.currentWordIndex = 0,
    this.progress = 0.0,
    this.error,
    this.durationMs,
    this.positionMs,
    this.autoProgress = true,
  });

  /// Create a copy with updated values
  NarrationState copyWith({
    NarrationStatus? status,
    String? currentText,
    int? currentWordIndex,
    double? progress,
    String? error,
    int? durationMs,
    int? positionMs,
    bool? autoProgress,
  }) {
    return NarrationState(
      status: status ?? this.status,
      currentText: currentText ?? this.currentText,
      currentWordIndex: currentWordIndex ?? this.currentWordIndex,
      progress: progress ?? this.progress,
      error: error ?? this.error,
      durationMs: durationMs ?? this.durationMs,
      positionMs: positionMs ?? this.positionMs,
      autoProgress: autoProgress ?? this.autoProgress,
    );
  }

  /// Create idle state
  static const NarrationState idle = NarrationState(status: NarrationStatus.idle);

  /// Create loading state
  static const NarrationState loading = NarrationState(status: NarrationStatus.loading);

  /// Create error state
  static NarrationState createError(String errorMessage) => NarrationState(
        status: NarrationStatus.error,
        error: errorMessage,
      );

  /// JSON serialization
  factory NarrationState.fromJson(Map<String, dynamic> json) => _$NarrationStateFromJson(json);
  Map<String, dynamic> toJson() => _$NarrationStateToJson(this);

  @override
  List<Object?> get props => [
        status,
        currentText,
        currentWordIndex,
        progress,
        error,
        durationMs,
        positionMs,
        autoProgress,
      ];
}

/// Configuration for narration playback
@JsonSerializable()
class NarrationConfig extends Equatable {
  /// Speech rate (0.1 to 2.0)
  final double speechRate;
  
  /// Speech pitch (0.5 to 2.0)
  final double speechPitch;
  
  /// Speech volume (0.0 to 1.0)
  final double speechVolume;
  
  /// Language code (e.g., 'en-US')
  final String language;
  
  /// Whether to automatically progress to next scene
  final bool autoProgress;
  
  /// Whether to highlight words during narration
  final bool highlightWords;
  
  /// Emotion cue for voice modulation
  final String emotionCue;
  
  /// Pause duration between sentences in milliseconds
  final int sentencePauseMs;
  
  /// Pause duration between words in milliseconds
  final int wordPauseMs;

  const NarrationConfig({
    this.speechRate = 0.5,
    this.speechPitch = 1.0,
    this.speechVolume = 1.0,
    this.language = 'en-US',
    this.autoProgress = false, // Rule 16: Default mode for story play is manual
    this.highlightWords = true,
    this.emotionCue = 'neutral',
    this.sentencePauseMs = 1000, // Rule 5: 1000ms pause between sentences
    this.wordPauseMs = 100,
  });

  /// Create a copy with updated values
  NarrationConfig copyWith({
    double? speechRate,
    double? speechPitch,
    double? speechVolume,
    String? language,
    bool? autoProgress,
    bool? highlightWords,
    String? emotionCue,
    int? sentencePauseMs,
    int? wordPauseMs,
  }) {
    return NarrationConfig(
      speechRate: speechRate ?? this.speechRate,
      speechPitch: speechPitch ?? this.speechPitch,
      speechVolume: speechVolume ?? this.speechVolume,
      language: language ?? this.language,
      autoProgress: autoProgress ?? this.autoProgress,
      highlightWords: highlightWords ?? this.highlightWords,
      emotionCue: emotionCue ?? this.emotionCue,
      sentencePauseMs: sentencePauseMs ?? this.sentencePauseMs,
      wordPauseMs: wordPauseMs ?? this.wordPauseMs,
    );
  }

  /// Default configuration
  static const NarrationConfig defaultConfig = NarrationConfig();

  /// JSON serialization
  factory NarrationConfig.fromJson(Map<String, dynamic> json) => _$NarrationConfigFromJson(json);
  Map<String, dynamic> toJson() => _$NarrationConfigToJson(this);

  @override
  List<Object?> get props => [
        speechRate,
        speechPitch,
        speechVolume,
        language,
        autoProgress,
        highlightWords,
        emotionCue,
        sentencePauseMs,
        wordPauseMs,
      ];
}

/// Represents a word highlight during narration
@JsonSerializable()
class WordHighlight extends Equatable {
  /// Start index of the word in the text
  final int startIndex;
  
  /// End index of the word in the text
  final int endIndex;
  
  /// The actual word text
  final String word;
  
  /// Start time of the word in milliseconds
  final int startTimeMs;
  
  /// End time of the word in milliseconds
  final int endTimeMs;
  
  /// Whether this word is currently being highlighted
  final bool isActive;
  
  /// Sentence index this word belongs to
  final int sentenceIndex;

  const WordHighlight({
    required this.startIndex,
    required this.endIndex,
    required this.word,
    required this.startTimeMs,
    required this.endTimeMs,
    this.isActive = false,
    this.sentenceIndex = 0,
  });

  /// Create a copy with updated values
  WordHighlight copyWith({
    int? startIndex,
    int? endIndex,
    String? word,
    int? startTimeMs,
    int? endTimeMs,
    bool? isActive,
    int? sentenceIndex,
  }) {
    return WordHighlight(
      startIndex: startIndex ?? this.startIndex,
      endIndex: endIndex ?? this.endIndex,
      word: word ?? this.word,
      startTimeMs: startTimeMs ?? this.startTimeMs,
      endTimeMs: endTimeMs ?? this.endTimeMs,
      isActive: isActive ?? this.isActive,
      sentenceIndex: sentenceIndex ?? this.sentenceIndex,
    );
  }

  /// Duration of the word in milliseconds
  int get durationMs => endTimeMs - startTimeMs;

  /// JSON serialization
  factory WordHighlight.fromJson(Map<String, dynamic> json) => _$WordHighlightFromJson(json);
  Map<String, dynamic> toJson() => _$WordHighlightToJson(this);

  @override
  List<Object?> get props => [
        startIndex,
        endIndex,
        word,
        startTimeMs,
        endTimeMs,
        isActive,
        sentenceIndex,
      ];
}

/// Progress tracking for narration
@JsonSerializable()
class NarrationProgress extends Equatable {
  /// Story ID this progress belongs to
  final String storyId;
  
  /// Scene ID this progress belongs to
  final String sceneId;
  
  /// Total number of words in the text
  final int totalWords;
  
  /// Number of words completed
  final int completedWords;
  
  /// Total number of sentences
  final int totalSentences;
  
  /// Number of sentences completed
  final int completedSentences;
  
  /// Total duration in milliseconds
  final int totalDurationMs;
  
  /// Current position in milliseconds
  final int currentPositionMs;
  
  /// Whether narration is completed
  final bool isCompleted;
  
  /// Timestamp when progress was last updated
  final DateTime lastUpdated;

  const NarrationProgress({
    required this.storyId,
    required this.sceneId,
    this.totalWords = 0,
    this.completedWords = 0,
    this.totalSentences = 0,
    this.completedSentences = 0,
    this.totalDurationMs = 0,
    this.currentPositionMs = 0,
    this.isCompleted = false,
    required this.lastUpdated,
  });

  /// Create a copy with updated values
  NarrationProgress copyWith({
    String? storyId,
    String? sceneId,
    int? totalWords,
    int? completedWords,
    int? totalSentences,
    int? completedSentences,
    int? totalDurationMs,
    int? currentPositionMs,
    bool? isCompleted,
    DateTime? lastUpdated,
  }) {
    return NarrationProgress(
      storyId: storyId ?? this.storyId,
      sceneId: sceneId ?? this.sceneId,
      totalWords: totalWords ?? this.totalWords,
      completedWords: completedWords ?? this.completedWords,
      totalSentences: totalSentences ?? this.totalSentences,
      completedSentences: completedSentences ?? this.completedSentences,
      totalDurationMs: totalDurationMs ?? this.totalDurationMs,
      currentPositionMs: currentPositionMs ?? this.currentPositionMs,
      isCompleted: isCompleted ?? this.isCompleted,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  /// Progress as percentage (0.0 to 1.0)
  double get progressPercentage {
    if (totalWords == 0) return 0.0;
    return completedWords / totalWords;
  }

  /// Time progress as percentage (0.0 to 1.0)
  double get timeProgressPercentage {
    if (totalDurationMs == 0) return 0.0;
    return currentPositionMs / totalDurationMs;
  }

  /// JSON serialization
  factory NarrationProgress.fromJson(Map<String, dynamic> json) => _$NarrationProgressFromJson(json);
  Map<String, dynamic> toJson() => _$NarrationProgressToJson(this);

  @override
  List<Object?> get props => [
        storyId,
        sceneId,
        totalWords,
        completedWords,
        totalSentences,
        completedSentences,
        totalDurationMs,
        currentPositionMs,
        isCompleted,
        lastUpdated,
      ];
}
