import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:logger/logger.dart';

/// Service for managing story playback settings
class StorySettingsService {
  static final Logger _logger = Logger();
  static StorySettingsService? _instance;
  
  SharedPreferences? _prefs;
  
  // Settings keys
  static const String _subtitleSizeKey = 'subtitle_size';
  static const String _narrationSpeedKey = 'narration_speed';
  static const String _subtitlesEnabledKey = 'subtitles_enabled';
  static const String _autoPlayKey = 'auto_play';
  static const String _controlRowTransparencyKey = 'control_row_transparency';
  static const String _textFontSizeKey = 'text_font_size';
  static const String _overlayScaleKey = 'overlay_scale';
  static const String _sceneTransitionDurationKey = 'scene_transition_duration';
  static const String _choiceTransitionDurationKey = 'choice_transition_duration';

  // Default values
  static const double _defaultSubtitleSize = 18.0;
  static const double _defaultNarrationSpeed = 0.35;
  static const bool _defaultSubtitlesEnabled = true;
  static const bool _defaultAutoPlay = false; // Rule 16: Default mode is manual
  static const double _defaultControlRowTransparency = 0.5; // 50%
  static const double _defaultTextFontSize = 18.0;
  static const double _defaultOverlayScale = 1.0; // 100% scale
  static const int _defaultSceneTransitionDuration = 1500; // milliseconds
  static const int _defaultChoiceTransitionDuration = 1200; // milliseconds
  
  // Current settings
  double _subtitleSize = _defaultSubtitleSize;
  double _narrationSpeed = _defaultNarrationSpeed;
  bool _subtitlesEnabled = _defaultSubtitlesEnabled;
  bool _autoPlay = _defaultAutoPlay;
  double _controlRowTransparency = _defaultControlRowTransparency;
  double _textFontSize = _defaultTextFontSize;
  double _overlayScale = _defaultOverlayScale;
  int _sceneTransitionDuration = _defaultSceneTransitionDuration;
  int _choiceTransitionDuration = _defaultChoiceTransitionDuration;
  
  // Stream controllers for settings changes
  final StreamController<double> _subtitleSizeController = StreamController<double>.broadcast();
  final StreamController<double> _narrationSpeedController = StreamController<double>.broadcast();
  final StreamController<bool> _subtitlesEnabledController = StreamController<bool>.broadcast();
  final StreamController<bool> _autoPlayController = StreamController<bool>.broadcast();
  final StreamController<double> _controlRowTransparencyController = StreamController<double>.broadcast();
  final StreamController<double> _textFontSizeController = StreamController<double>.broadcast();
  final StreamController<double> _overlayScaleController = StreamController<double>.broadcast();
  final StreamController<int> _sceneTransitionDurationController = StreamController<int>.broadcast();
  final StreamController<int> _choiceTransitionDurationController = StreamController<int>.broadcast();
  
  // Private constructor
  StorySettingsService._();
  
  /// Get singleton instance
  static StorySettingsService get instance {
    _instance ??= StorySettingsService._();
    return _instance!;
  }
  
  // Getters for streams
  Stream<double> get subtitleSizeStream => _subtitleSizeController.stream;
  Stream<double> get narrationSpeedStream => _narrationSpeedController.stream;
  Stream<bool> get subtitlesEnabledStream => _subtitlesEnabledController.stream;
  Stream<bool> get autoPlayStream => _autoPlayController.stream;
  Stream<double> get controlRowTransparencyStream => _controlRowTransparencyController.stream;
  Stream<double> get textFontSizeStream => _textFontSizeController.stream;
  Stream<double> get overlayScaleStream => _overlayScaleController.stream;
  Stream<int> get sceneTransitionDurationStream => _sceneTransitionDurationController.stream;
  Stream<int> get choiceTransitionDurationStream => _choiceTransitionDurationController.stream;

  // Getters for current values
  double get subtitleSize => _subtitleSize;
  double get narrationSpeed => _narrationSpeed;
  bool get subtitlesEnabled => _subtitlesEnabled;
  bool get autoPlay => _autoPlay;
  double get controlRowTransparency => _controlRowTransparency;
  double get textFontSize => _textFontSize;
  double get overlayScale => _overlayScale;
  int get sceneTransitionDuration => _sceneTransitionDuration;
  int get choiceTransitionDuration => _choiceTransitionDuration;

  /// Initialize the settings service
  Future<void> initialize() async {
    try {
      _logger.i('[StorySettingsService] Initializing settings service');
      
      _prefs = await SharedPreferences.getInstance();
      await _loadSettings();
      
      _logger.i('[StorySettingsService] Settings service initialized successfully');
      
    } catch (e) {
      _logger.e('[StorySettingsService] Failed to initialize settings service: $e');
      // Use default values if initialization fails
      _setDefaultValues();
    }
  }

  /// Load settings from SharedPreferences
  Future<void> _loadSettings() async {
    if (_prefs == null) return;

    _subtitleSize = _prefs!.getDouble(_subtitleSizeKey) ?? _defaultSubtitleSize;
    _narrationSpeed = _prefs!.getDouble(_narrationSpeedKey) ?? _defaultNarrationSpeed;
    _subtitlesEnabled = _prefs!.getBool(_subtitlesEnabledKey) ?? _defaultSubtitlesEnabled;
    _autoPlay = _prefs!.getBool(_autoPlayKey) ?? _defaultAutoPlay;
    _controlRowTransparency = _prefs!.getDouble(_controlRowTransparencyKey) ?? _defaultControlRowTransparency;
    _textFontSize = _prefs!.getDouble(_textFontSizeKey) ?? _defaultTextFontSize;
    _overlayScale = _prefs!.getDouble(_overlayScaleKey) ?? _defaultOverlayScale;
    _sceneTransitionDuration = _prefs!.getInt(_sceneTransitionDurationKey) ?? _defaultSceneTransitionDuration;
    _choiceTransitionDuration = _prefs!.getInt(_choiceTransitionDurationKey) ?? _defaultChoiceTransitionDuration;

    _logger.d('[StorySettingsService] Settings loaded - Subtitle size: $_subtitleSize, Narration speed: $_narrationSpeed, Subtitles enabled: $_subtitlesEnabled, Auto play: $_autoPlay, Control transparency: $_controlRowTransparency, Text font size: $_textFontSize, Overlay scale: $_overlayScale, Scene transition: $_sceneTransitionDuration ms, Choice transition: $_choiceTransitionDuration ms');
  }

  /// Set default values
  void _setDefaultValues() {
    _subtitleSize = _defaultSubtitleSize;
    _narrationSpeed = _defaultNarrationSpeed;
    _subtitlesEnabled = _defaultSubtitlesEnabled;
    _autoPlay = _defaultAutoPlay;
    _controlRowTransparency = _defaultControlRowTransparency;
    _textFontSize = _defaultTextFontSize;
    _overlayScale = _defaultOverlayScale;
    _sceneTransitionDuration = _defaultSceneTransitionDuration;
    _choiceTransitionDuration = _defaultChoiceTransitionDuration;
  }

  /// Update subtitle size (12.0 to 32.0)
  Future<void> setSubtitleSize(double size) async {
    final clampedSize = size.clamp(12.0, 32.0);
    
    if (_subtitleSize != clampedSize) {
      _subtitleSize = clampedSize;
      _subtitleSizeController.add(_subtitleSize);
      
      await _saveSubtitleSize();
      _logger.d('[StorySettingsService] Subtitle size updated to: $_subtitleSize');
    }
  }

  /// Update narration speed (0.1 to 1.0)
  Future<void> setNarrationSpeed(double speed) async {
    final clampedSpeed = speed.clamp(0.1, 1.0);
    
    if (_narrationSpeed != clampedSpeed) {
      _narrationSpeed = clampedSpeed;
      _narrationSpeedController.add(_narrationSpeed);
      
      await _saveNarrationSpeed();
      _logger.d('[StorySettingsService] Narration speed updated to: $_narrationSpeed');
    }
  }

  /// Toggle subtitles enabled/disabled
  Future<void> setSubtitlesEnabled(bool enabled) async {
    if (_subtitlesEnabled != enabled) {
      _subtitlesEnabled = enabled;
      _subtitlesEnabledController.add(_subtitlesEnabled);
      
      await _saveSubtitlesEnabled();
      _logger.d('[StorySettingsService] Subtitles enabled updated to: $_subtitlesEnabled');
    }
  }

  /// Toggle auto play enabled/disabled
  Future<void> setAutoPlay(bool enabled) async {
    if (_autoPlay != enabled) {
      _autoPlay = enabled;
      _autoPlayController.add(_autoPlay);

      await _saveAutoPlay();
      _logger.d('[StorySettingsService] Auto play updated to: $_autoPlay');
    }
  }

  /// Set auto scene progression (alias for autoPlay for clarity)
  Future<void> setAutoSceneProgression(bool enabled) async {
    await setAutoPlay(enabled);
  }

  /// Get auto scene progression setting
  bool get autoSceneProgression => _autoPlay;

  /// Update control row transparency (0.0 to 1.0)
  Future<void> setControlRowTransparency(double transparency) async {
    final clampedTransparency = transparency.clamp(0.0, 1.0);

    if (_controlRowTransparency != clampedTransparency) {
      _controlRowTransparency = clampedTransparency;
      _controlRowTransparencyController.add(_controlRowTransparency);

      await _saveControlRowTransparency();
      _logger.d('[StorySettingsService] Control row transparency updated to: $_controlRowTransparency');
    }
  }

  /// Update text font size (12.0 to 32.0)
  Future<void> setTextFontSize(double size) async {
    final clampedSize = size.clamp(12.0, 32.0);

    if (_textFontSize != clampedSize) {
      _textFontSize = clampedSize;
      _textFontSizeController.add(_textFontSize);

      await _saveTextFontSize();
      _logger.d('[StorySettingsService] Text font size updated to: $_textFontSize');
    }
  }

  /// Update overlay scale (0.8 to 1.5)
  Future<void> setOverlayScale(double scale) async {
    final clampedScale = scale.clamp(0.8, 1.5);

    if (_overlayScale != clampedScale) {
      _overlayScale = clampedScale;
      _overlayScaleController.add(_overlayScale);

      await _saveOverlayScale();
      _logger.d('[StorySettingsService] Overlay scale updated to: $_overlayScale');
    }
  }

  /// Update scene transition duration (500 to 3000 milliseconds)
  Future<void> setSceneTransitionDuration(int duration) async {
    final clampedDuration = duration.clamp(500, 3000);

    if (_sceneTransitionDuration != clampedDuration) {
      _sceneTransitionDuration = clampedDuration;
      _sceneTransitionDurationController.add(_sceneTransitionDuration);

      await _saveSceneTransitionDuration();
      _logger.d('[StorySettingsService] Scene transition duration updated to: $_sceneTransitionDuration ms');
    }
  }

  /// Update choice transition duration (500 to 2500 milliseconds)
  Future<void> setChoiceTransitionDuration(int duration) async {
    final clampedDuration = duration.clamp(500, 2500);

    if (_choiceTransitionDuration != clampedDuration) {
      _choiceTransitionDuration = clampedDuration;
      _choiceTransitionDurationController.add(_choiceTransitionDuration);

      await _saveChoiceTransitionDuration();
      _logger.d('[StorySettingsService] Choice transition duration updated to: $_choiceTransitionDuration ms');
    }
  }

  /// Save subtitle size to SharedPreferences
  Future<void> _saveSubtitleSize() async {
    try {
      await _prefs?.setDouble(_subtitleSizeKey, _subtitleSize);
    } catch (e) {
      _logger.e('[StorySettingsService] Failed to save subtitle size: $e');
    }
  }

  /// Save narration speed to SharedPreferences
  Future<void> _saveNarrationSpeed() async {
    try {
      await _prefs?.setDouble(_narrationSpeedKey, _narrationSpeed);
    } catch (e) {
      _logger.e('[StorySettingsService] Failed to save narration speed: $e');
    }
  }

  /// Save subtitles enabled to SharedPreferences
  Future<void> _saveSubtitlesEnabled() async {
    try {
      await _prefs?.setBool(_subtitlesEnabledKey, _subtitlesEnabled);
    } catch (e) {
      _logger.e('[StorySettingsService] Failed to save subtitles enabled: $e');
    }
  }

  /// Save auto play to SharedPreferences
  Future<void> _saveAutoPlay() async {
    try {
      await _prefs?.setBool(_autoPlayKey, _autoPlay);
    } catch (e) {
      _logger.e('[StorySettingsService] Failed to save auto play: $e');
    }
  }

  /// Save control row transparency to SharedPreferences
  Future<void> _saveControlRowTransparency() async {
    try {
      await _prefs?.setDouble(_controlRowTransparencyKey, _controlRowTransparency);
    } catch (e) {
      _logger.e('[StorySettingsService] Failed to save control row transparency: $e');
    }
  }

  /// Save text font size to SharedPreferences
  Future<void> _saveTextFontSize() async {
    try {
      await _prefs?.setDouble(_textFontSizeKey, _textFontSize);
    } catch (e) {
      _logger.e('[StorySettingsService] Failed to save text font size: $e');
    }
  }

  /// Save overlay scale to SharedPreferences
  Future<void> _saveOverlayScale() async {
    try {
      await _prefs?.setDouble(_overlayScaleKey, _overlayScale);
    } catch (e) {
      _logger.e('[StorySettingsService] Failed to save overlay scale: $e');
    }
  }

  /// Save scene transition duration to SharedPreferences
  Future<void> _saveSceneTransitionDuration() async {
    try {
      await _prefs?.setInt(_sceneTransitionDurationKey, _sceneTransitionDuration);
    } catch (e) {
      _logger.e('[StorySettingsService] Failed to save scene transition duration: $e');
    }
  }

  /// Save choice transition duration to SharedPreferences
  Future<void> _saveChoiceTransitionDuration() async {
    try {
      await _prefs?.setInt(_choiceTransitionDurationKey, _choiceTransitionDuration);
    } catch (e) {
      _logger.e('[StorySettingsService] Failed to save choice transition duration: $e');
    }
  }

  /// Reset all settings to defaults
  Future<void> resetToDefaults() async {
    _logger.i('[StorySettingsService] Resetting all settings to defaults');

    await setSubtitleSize(_defaultSubtitleSize);
    await setNarrationSpeed(_defaultNarrationSpeed);
    await setSubtitlesEnabled(_defaultSubtitlesEnabled);
    await setAutoPlay(_defaultAutoPlay);
    await setControlRowTransparency(_defaultControlRowTransparency);
    await setTextFontSize(_defaultTextFontSize);
    await setOverlayScale(_defaultOverlayScale);
    await setSceneTransitionDuration(_defaultSceneTransitionDuration);
    await setChoiceTransitionDuration(_defaultChoiceTransitionDuration);

    _logger.i('[StorySettingsService] All settings reset to defaults');
  }

  /// Get settings as a map for debugging
  Map<String, dynamic> getSettingsMap() {
    return {
      'subtitleSize': _subtitleSize,
      'narrationSpeed': _narrationSpeed,
      'subtitlesEnabled': _subtitlesEnabled,
      'autoPlay': _autoPlay,
      'controlRowTransparency': _controlRowTransparency,
      'textFontSize': _textFontSize,
      'overlayScale': _overlayScale,
      'sceneTransitionDuration': _sceneTransitionDuration,
      'choiceTransitionDuration': _choiceTransitionDuration,
    };
  }

  /// Dispose of resources
  void dispose() {
    _subtitleSizeController.close();
    _narrationSpeedController.close();
    _subtitlesEnabledController.close();
    _autoPlayController.close();
    _controlRowTransparencyController.close();
    _textFontSizeController.close();
    _overlayScaleController.close();
    _sceneTransitionDurationController.close();
    _choiceTransitionDurationController.close();
    _logger.i('[StorySettingsService] Service disposed');
  }
}
