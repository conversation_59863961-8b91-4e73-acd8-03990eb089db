import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Service for scanning and loading stories from the assets/stories folder
class StoryScannerService {
  static final StoryScannerService _instance = StoryScannerService._internal();
  factory StoryScannerService() => _instance;
  StoryScannerService._internal();

  static const String _storiesBasePath = 'assets/stories';
  static const String _logPrefix = '[StoryScannerService]';

  List<String> _availableStoryIds = [];
  Map<String, EnhancedStoryModel> _loadedStories = {};
  bool _isInitialized = false;

  /// Get list of available story IDs
  List<String> get availableStoryIds => List.unmodifiable(_availableStoryIds);

  /// Get loaded stories
  Map<String, EnhancedStoryModel> get loadedStories => Map.unmodifiable(_loadedStories);

  /// Check if service is initialized
  bool get isInitialized => _isInitialized;

  /// Initialize the story scanner service
  Future<void> initialize() async {
    if (_isInitialized) {
      AppLogger.debug('$_logPrefix: Service already initialized');
      return;
    }

    try {
      AppLogger.info('$_logPrefix: Initializing story scanner service');
      await _scanStoriesFolder();
      _isInitialized = true;
      AppLogger.info('$_logPrefix: Service initialized successfully with ${_availableStoryIds.length} stories');
    } catch (e) {
      AppLogger.error('$_logPrefix: Failed to initialize service', e);
      rethrow;
    }
  }

  /// Scan the assets/stories folder for available stories
  Future<void> _scanStoriesFolder() async {
    try {
      AppLogger.debug('$_logPrefix: Scanning stories folder');
      
      // Get the asset manifest to find story folders
      final manifestContent = await rootBundle.loadString('AssetManifest.json');
      final Map<String, dynamic> manifestMap = json.decode(manifestContent);
      
      // Find all story.json files in the assets/stories directory
      final storyPaths = manifestMap.keys
          .where((String key) => key.startsWith('$_storiesBasePath/') && key.endsWith('/story.json'))
          .toList();

      AppLogger.debug('$_logPrefix: Found ${storyPaths.length} story.json files');

      // Extract story IDs from paths
      _availableStoryIds.clear();
      for (final path in storyPaths) {
        final storyId = _extractStoryIdFromPath(path);
        if (storyId != null) {
          _availableStoryIds.add(storyId);
          AppLogger.debug('$_logPrefix: Found story: $storyId');
        }
      }

      // Sort story IDs for consistent ordering
      _availableStoryIds.sort();
      
      AppLogger.info('$_logPrefix: Scanned ${_availableStoryIds.length} stories: $_availableStoryIds');
      
    } catch (e) {
      AppLogger.error('$_logPrefix: Error scanning stories folder', e);
      rethrow;
    }
  }

  /// Extract story ID from asset path
  String? _extractStoryIdFromPath(String path) {
    // Path format: assets/stories/storyXXX/story.json
    final parts = path.split('/');
    if (parts.length >= 3 && parts[0] == 'assets' && parts[1] == 'stories') {
      return parts[2]; // This should be the story ID (e.g., story013)
    }
    return null;
  }

  /// Load a specific story by ID
  Future<EnhancedStoryModel?> loadStory(String storyId) async {
    if (!_isInitialized) {
      await initialize();
    }

    if (_loadedStories.containsKey(storyId)) {
      AppLogger.debug('$_logPrefix: Story $storyId already loaded from cache');
      return _loadedStories[storyId];
    }

    if (!_availableStoryIds.contains(storyId)) {
      AppLogger.warning('$_logPrefix: Story $storyId not found in available stories');
      return null;
    }

    try {
      AppLogger.debug('$_logPrefix: Loading story $storyId');
      
      final storyPath = '$_storiesBasePath/$storyId/story.json';
      final storyContent = await rootBundle.loadString(storyPath);
      final storyJson = json.decode(storyContent) as Map<String, dynamic>;
      
      final story = EnhancedStoryModel.fromJson(storyJson);
      _loadedStories[storyId] = story;
      
      AppLogger.info('$_logPrefix: Successfully loaded story $storyId: ${story.title}');
      return story;
      
    } catch (e) {
      AppLogger.error('$_logPrefix: Failed to load story $storyId', e);
      return null;
    }
  }

  /// Load all available stories
  Future<List<EnhancedStoryModel>> loadAllStories() async {
    if (!_isInitialized) {
      await initialize();
    }

    final stories = <EnhancedStoryModel>[];
    
    for (final storyId in _availableStoryIds) {
      final story = await loadStory(storyId);
      if (story != null) {
        stories.add(story);
      }
    }
    
    AppLogger.info('$_logPrefix: Loaded ${stories.length} stories out of ${_availableStoryIds.length} available');
    return stories;
  }

  /// Get story metadata without loading full story
  Future<Map<String, dynamic>?> getStoryMetadata(String storyId) async {
    if (!_isInitialized) {
      await initialize();
    }

    if (!_availableStoryIds.contains(storyId)) {
      return null;
    }

    try {
      final storyPath = '$_storiesBasePath/$storyId/story.json';
      final storyContent = await rootBundle.loadString(storyPath);
      final storyJson = json.decode(storyContent) as Map<String, dynamic>;
      
      // Return only metadata fields
      return {
        'story_id': storyJson['story_id'],
        'title': storyJson['title'],
        'age_group': storyJson['age_group'],
        'difficulty': storyJson['difficulty'],
        'moral': storyJson['moral'],
        'estimated_time': storyJson['estimated_time'],
        'cover_image': storyJson['cover_image'],
      };
      
    } catch (e) {
      AppLogger.error('$_logPrefix: Failed to get metadata for story $storyId', e);
      return null;
    }
  }

  /// Refresh the story list (re-scan the folder)
  Future<void> refresh() async {
    AppLogger.info('$_logPrefix: Refreshing story list');
    _loadedStories.clear();
    await _scanStoriesFolder();
  }

  /// Check if a story exists
  bool hasStory(String storyId) {
    return _availableStoryIds.contains(storyId);
  }

  /// Get story count
  int get storyCount => _availableStoryIds.length;

  /// Clear loaded stories from memory
  void clearCache() {
    AppLogger.debug('$_logPrefix: Clearing story cache');
    _loadedStories.clear();
  }

  /// Get stories by age group
  Future<List<EnhancedStoryModel>> getStoriesByAgeGroup(String ageGroup) async {
    final allStories = await loadAllStories();
    return allStories.where((story) => story.ageGroup == ageGroup).toList();
  }

  /// Get stories by difficulty
  Future<List<EnhancedStoryModel>> getStoriesByDifficulty(String difficulty) async {
    final allStories = await loadAllStories();
    return allStories.where((story) => story.difficulty == difficulty).toList();
  }

  /// Get stories by moral theme
  Future<List<EnhancedStoryModel>> getStoriesByMoral(String moral) async {
    final allStories = await loadAllStories();
    return allStories.where((story) => story.moral == moral).toList();
  }
}
