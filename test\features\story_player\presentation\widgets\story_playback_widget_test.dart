import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:choice_once_upon_a_time/features/story_player/presentation/widgets/story_playback_widget.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart';
import 'package:choice_once_upon_a_time/core/services/story_narration_service_interface.dart';
import 'package:choice_once_upon_a_time/core/services/story_settings_service.dart';

import 'story_playback_widget_test.mocks.dart';

@GenerateMocks([IStoryNarrationService, StorySettingsService])
void main() {
  group('StoryPlaybackWidget Loading Progress Tests', () {
    late MockIStoryNarrationService mockNarrationService;
    late MockStorySettingsService mockSettingsService;
    late EnhancedStoryModel testStory;
    late EnhancedSceneModel testScene;

    setUp(() {
      mockNarrationService = MockIStoryNarrationService();
      mockSettingsService = MockStorySettingsService();

      // Create test story and scene with correct structure
      testStory = EnhancedStoryModel(
        storyId: 'test_story',
        ageGroup: '3-6',
        difficulty: 'easy',
        title: 'Test Story',
        moral: 'Test moral',
        coverImage: 'test_cover.jpg',
        setup: StorySetupModel(
          setting: 'Test setting',
          tone: 'friendly',
          context: 'Test context',
        ),
        narratorProfile: NarratorProfileModel(
          name: 'Test Narrator',
          voice: VoiceModel(pitch: 1.0, rate: 1.0, volume: 1.0),
        ),
        characters: [],
        scenes: [],
        postStory: PostStoryModel(),
      );

      testScene = EnhancedSceneModel(
        id: 'test_scene',
        text: 'This is a test scene with some narration text.',
        speaker: 'narrator',
        emotion: 'neutral',
        image: 'test_scene.jpg',
        pauseDuration: 1000,
        choices: [],
        next: null,
      );

      // Setup mock defaults
      when(mockSettingsService.autoPlay).thenReturn(false);
      when(mockSettingsService.sceneTransitionDuration).thenReturn(2000);
      when(mockNarrationService.progressStream).thenAnswer((_) => Stream.empty());
      when(mockNarrationService.stateStream).thenAnswer((_) => Stream.empty());
    });

    testWidgets('should show black screen during image loading, not loading indicator', (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StoryPlaybackWidget(
              story: testStory,
              scene: testScene,
              onChoiceSelected: (choice) {},
              narrationService: mockNarrationService,
              settingsService: mockSettingsService,
            ),
          ),
        ),
      );

      // Initially, the widget should show a black screen while loading
      // NOT a loading indicator with CircularProgressIndicator
      expect(find.byType(CircularProgressIndicator), findsNothing);
      expect(find.byType(Container), findsWidgets);
      
      // Verify that the container has black color (indicating loading state)
      final Container container = tester.widget(find.byType(Container).first);
      expect(container.color, equals(Colors.black));

      // Verify no "Loading Scene..." text is shown
      expect(find.text('Loading Scene...'), findsNothing);
    });

    testWidgets('should show loading progress only when user triggers scene transition after narration', (WidgetTester tester) async {
      // Create a scene with choices to test the transition loading
      final sceneWithChoices = EnhancedSceneModel(
        id: 'test_scene_with_choices',
        text: 'This is a test scene with choices.',
        speaker: 'narrator',
        emotion: 'neutral',
        image: 'test_scene.jpg',
        pauseDuration: 1000,
        choices: [
          ChoiceOptionModel(
            option: 'Test Choice',
            visual: 'choice_icon.png',
            next: 'next_scene',
          ),
        ],
        next: null,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StoryPlaybackWidget(
              story: testStory,
              scene: sceneWithChoices,
              onChoiceSelected: (choice) {},
              narrationService: mockNarrationService,
              settingsService: mockSettingsService,
            ),
          ),
        ),
      );

      // Wait for the widget to build and load
      await tester.pumpAndSettle();

      // Initially, no loading dialog should be present
      expect(find.text('Loading next scene...'), findsNothing);
      expect(find.text('Processing your choice...'), findsNothing);

      // Note: In a real scenario, we would need to simulate the narration completion
      // and then test the loading behavior. For this test, we're verifying that
      // no loading indicators are shown during the initial loading phase.
    });

    testWidgets('should not show CircularProgressIndicator during initial scene loading', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StoryPlaybackWidget(
              story: testStory,
              scene: testScene,
              onChoiceSelected: (choice) {},
              narrationService: mockNarrationService,
              settingsService: mockSettingsService,
            ),
          ),
        ),
      );

      // Pump multiple frames to simulate loading
      await tester.pump();
      await tester.pump(const Duration(milliseconds: 100));
      await tester.pump(const Duration(milliseconds: 500));

      // At no point during initial loading should we see a CircularProgressIndicator
      expect(find.byType(CircularProgressIndicator), findsNothing);
      
      // We should also not see any "Loading Scene..." text
      expect(find.text('Loading Scene...'), findsNothing);
    });
  });
}
