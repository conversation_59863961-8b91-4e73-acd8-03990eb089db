# Navigation Flow and Routing

## GoRouter Configuration

```mermaid
graph TD
    subgraph AppRoutes["Application Routes"]
        Root[/ - Launch Screen]
        FTUE[/ftue - First Time User Experience]
        ProfileSelection[/profile-selection - Profile Selection]
        Welcome[/welcome - Welcome Screen]
        Home[/home - Home Screen]
        StoryLibrary[/story-library - Story Library]
        StoryPlayer[/story/:storyId - Story Player]
        ParentGate[/parent-gate - Parent Gate]
        ParentAuth[/parent-auth - Parent Authentication]
        ParentDashboard[/parent-dashboard - Parent Dashboard]
        ProgressTracking[/parent-dashboard/progress - Progress Tracking]
        Settings[/parent-dashboard/settings - Settings]
        ProfileManagement[/parent-dashboard/profiles - Profile Management]
    end
    
    Root --> FTUE
    Root --> Home
    FTUE --> ProfileSelection
    ProfileSelection --> Welcome
    Welcome --> Home
    Home --> StoryLibrary
    Home --> ParentGate
    StoryLibrary --> StoryPlayer
    ParentGate --> ParentAuth
    ParentAuth --> ParentDashboard
    ParentDashboard --> ProgressTracking
    ParentDashboard --> Settings
    ParentDashboard --> ProfileManagement
    
    style Root fill:#e8f5e8
    style Home fill:#e3f2fd
    style StoryPlayer fill:#fff3e0
    style ParentDashboard fill:#fce4ec
```

## Screen Hierarchy and Back Navigation

```mermaid
graph TD
    subgraph Level1["Level 1 - Entry Points"]
        Launch[Launch Screen]
    end
    
    subgraph Level2["Level 2 - Main Navigation"]
        FTUE[FTUE Screen]
        Home[Home Screen]
    end
    
    subgraph Level3["Level 3 - Feature Entry"]
        StoryLibrary[Story Library]
        ParentGate[Parent Gate]
    end
    
    subgraph Level4["Level 4 - Feature Screens"]
        StoryIntro[Story Introduction]
        ParentAuth[Parent Authentication]
    end
    
    subgraph Level5["Level 5 - Deep Features"]
        StoryPlayer[Story Player]
        ParentDashboard[Parent Dashboard]
    end
    
    subgraph Level6["Level 6 - Sub Features"]
        CharacterProfiles[Character Profiles]
        ProgressTracking[Progress Tracking]
        Settings[Settings]
        ProfileManagement[Profile Management]
    end
    
    Launch --> FTUE
    Launch --> Home
    FTUE --> Home
    Home --> StoryLibrary
    Home --> ParentGate
    StoryLibrary --> StoryIntro
    ParentGate --> ParentAuth
    StoryIntro --> StoryPlayer
    ParentAuth --> ParentDashboard
    StoryPlayer --> CharacterProfiles
    ParentDashboard --> ProgressTracking
    ParentDashboard --> Settings
    ParentDashboard --> ProfileManagement
    
    %% Back Navigation (dotted lines)
    FTUE -.-> Launch
    Home -.-> Launch
    StoryLibrary -.-> Home
    ParentGate -.-> Home
    StoryIntro -.-> StoryLibrary
    ParentAuth -.-> ParentGate
    StoryPlayer -.-> StoryIntro
    ParentDashboard -.-> ParentAuth
    CharacterProfiles -.-> StoryPlayer
    ProgressTracking -.-> ParentDashboard
    Settings -.-> ParentDashboard
    ProfileManagement -.-> ParentDashboard
    
    style Level1 fill:#e8eaf6
    style Level2 fill:#e0f2f1
    style Level3 fill:#fff8e1
    style Level4 fill:#fce4ec
    style Level5 fill:#f3e5f5
    style Level6 fill:#e8f5e8
```

## Story Player Navigation States

```mermaid
stateDiagram-v2
    [*] --> Loading
    Loading --> Welcome : Story Loaded
    Loading --> Error : Load Failed
    
    Welcome --> CharacterProfiles : Continue
    CharacterProfiles --> StoryPlayback : Start Story
    
    state StoryPlayback {
        [*] --> SceneLoading
        SceneLoading --> SceneNarration : Image Loaded
        SceneNarration --> ChoiceSelection : Has Choices
        SceneNarration --> NextScene : No Choices
        ChoiceSelection --> SceneTransition : Choice Made
        NextScene --> SceneTransition : Next Tapped
        SceneTransition --> SceneLoading : More Scenes
        SceneTransition --> StoryComplete : Final Scene
    }
    
    StoryPlayback --> StoryComplete : Story Finished
    StoryComplete --> [*] : Exit to Library
    
    %% Back button handling
    Welcome --> [*] : Back to Library
    CharacterProfiles --> Welcome : Back
    StoryPlayback --> CalmExit : Back During Story
    CalmExit --> [*] : Confirm Exit
    CalmExit --> StoryPlayback : Resume Story
    
    Error --> [*] : Back to Library
```

## Parent Zone Navigation Flow

```mermaid
flowchart TD
    HomeScreen[Home Screen] --> ParentGateButton[Parent Gate Button]
    ParentGateButton --> ParentGate[Parent Gate Screen]
    
    ParentGate --> AuthMethod{Authentication Method}
    AuthMethod -->|PIN| PINEntry[PIN Entry]
    AuthMethod -->|Pattern| PatternEntry[Pattern Entry]
    AuthMethod -->|Biometric| BiometricAuth[Biometric Authentication]
    
    PINEntry --> ValidateAuth{Valid Auth?}
    PatternEntry --> ValidateAuth
    BiometricAuth --> ValidateAuth
    
    ValidateAuth -->|No| AuthFailed[Authentication Failed]
    ValidateAuth -->|Yes| ParentDashboard[Parent Dashboard]
    
    AuthFailed --> ParentGate
    
    ParentDashboard --> DashboardOptions{Select Option}
    DashboardOptions --> ProgressTracking[Progress Tracking]
    DashboardOptions --> Settings[Settings & Controls]
    DashboardOptions --> ProfileManagement[Profile Management]
    DashboardOptions --> ExitParentZone[Exit Parent Zone]
    
    ProgressTracking --> ProgressDetails[View Progress Details]
    ProgressDetails --> ParentDashboard
    
    Settings --> SettingsCategories{Settings Category}
    SettingsCategories --> AppSettings[App Settings]
    SettingsCategories --> StorySettings[Story Settings]
    SettingsCategories --> ParentalControls[Parental Controls]
    AppSettings --> Settings
    StorySettings --> Settings
    ParentalControls --> Settings
    Settings --> ParentDashboard
    
    ProfileManagement --> ProfileActions{Profile Action}
    ProfileActions --> CreateProfile[Create New Profile]
    ProfileActions --> EditProfile[Edit Existing Profile]
    ProfileActions --> DeleteProfile[Delete Profile]
    CreateProfile --> ProfileManagement
    EditProfile --> ProfileManagement
    DeleteProfile --> ProfileManagement
    ProfileManagement --> ParentDashboard
    
    ExitParentZone --> HomeScreen
    
    style HomeScreen fill:#e8f5e8
    style ParentDashboard fill:#e3f2fd
    style ProgressTracking fill:#fff3e0
    style Settings fill:#fce4ec
```
