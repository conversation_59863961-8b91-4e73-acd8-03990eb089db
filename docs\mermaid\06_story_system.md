# Story System Architecture

## Story Loading and Playback Flow

```mermaid
flowchart TD
    Start([User Selects Story]) --> LoadMetadata[Load Story Metadata]
    LoadMetadata --> CheckCache{Story in Cache?}
    
    CheckCache -->|Yes| LoadFromCache[Load from Cache]
    CheckCache -->|No| LoadFromAssets[Load from Assets]
    
    LoadFromCache --> ParseStory[Parse Story JSON]
    LoadFromAssets --> ParseStory
    
    ParseStory --> ValidateStory{Valid Story?}
    ValidateStory -->|No| ShowError[Show Error Message]
    ValidateStory -->|Yes| InitializePlayer[Initialize Story Player]
    
    InitializePlayer --> ShowWelcome[Show Welcome Screen]
    ShowWelcome --> ShowCharacters[Show Character Profiles]
    ShowCharacters --> LoadFirstScene[Load First Scene]
    
    LoadFirstScene --> PreloadImage[Preload Scene Image]
    PreloadImage --> InitializeTTS[Initialize TTS Service]
    InitializeTTS --> StartNarration[Start Scene Narration]
    
    StartNarration --> WordByWord[Word-by-Word Highlighting]
    WordByWord --> NarrationComplete{Narration Complete?}
    
    NarrationComplete -->|No| WordByWord
    NarrationComplete -->|Yes| CheckChoices{Scene Has Choices?}
    
    CheckChoices -->|Yes| ShowChoices[Show Choice Buttons]
    CheckChoices -->|No| ShowNext[Show Next Button]
    
    ShowChoices --> UserChoice[User Selects Choice]
    ShowNext --> UserNext[User Taps Next]
    
    UserChoice --> ProcessChoice[Process Choice Selection]
    UserNext --> ProcessNext[Process Next Scene]
    
    ProcessChoice --> NextScene[Navigate to Next Scene]
    ProcessNext --> NextScene
    
    NextScene --> StoryEnd{Story Complete?}
    StoryEnd -->|No| LoadFirstScene
    StoryEnd -->|Yes| ShowCompletion[Show Completion Screen]
    
    ShowCompletion --> SaveProgress[Save Progress]
    SaveProgress --> ShowRewards[Show Rewards]
    ShowRewards --> End([Return to Library])
    
    style Start fill:#e8f5e8
    style WordByWord fill:#fff3e0
    style ShowChoices fill:#e3f2fd
    style ShowCompletion fill:#fce4ec
```

## Story Data Structure

```mermaid
graph TD
    subgraph StoryAssets["Story Assets Structure"]
        StoryFolder[assets/stories/story001/]
        StoryJSON[story.json]
        ImagesFolder[images/]
        AudioFolder[audio/]
        
        StoryFolder --> StoryJSON
        StoryFolder --> ImagesFolder
        StoryFolder --> AudioFolder
        
        ImagesFolder --> CoverImage[cover.jpg]
        ImagesFolder --> SceneImages[scene_*.jpg]
        ImagesFolder --> CharacterImages[character_*.png]
        
        AudioFolder --> BackgroundMusic[background.mp3]
        AudioFolder --> SoundEffects[effects/]
    end
    
    subgraph StoryContent["Story Content Model"]
        Story[Enhanced Story Model]
        Setup[Story Setup]
        Narrator[Narrator Profile]
        Characters[Character List]
        Scenes[Scene List]
        
        Story --> Setup
        Story --> Narrator
        Story --> Characters
        Story --> Scenes
        
        Scenes --> Scene1[Scene 1]
        Scenes --> Scene2[Scene 2]
        Scenes --> SceneN[Scene N]
        
        Scene1 --> Choices1[Choice Options]
        Scene2 --> Choices2[Choice Options]
    end
    
    StoryJSON -.-> Story
    SceneImages -.-> Scenes
    CharacterImages -.-> Characters
    
    style StoryAssets fill:#e8eaf6
    style StoryContent fill:#e0f2f1
```

## Narration System Flow

```mermaid
sequenceDiagram
    participant UI as Story Player UI
    participant NS as Narration Service
    participant TTS as TTS Service
    participant VG as Voice Guidance
    participant Settings as Settings Service
    
    UI->>NS: Start Scene Narration
    NS->>Settings: Get Narration Settings
    Settings-->>NS: Return Settings (speed, volume, etc.)
    
    NS->>TTS: Initialize with Settings
    TTS-->>NS: TTS Ready
    
    NS->>VG: Play Voice Guide (if enabled)
    VG-->>NS: Voice Guide Complete
    
    NS->>TTS: Start Sentence Narration
    TTS->>UI: Word Highlight Events
    UI->>UI: Highlight Current Word
    
    TTS-->>NS: Sentence Complete
    NS->>NS: Move to Next Sentence
    
    alt More Sentences
        NS->>TTS: Start Next Sentence
    else All Sentences Complete
        NS->>UI: Narration Complete Event
        UI->>UI: Show Choices/Next Button
    end
    
    UI->>NS: User Interaction (pause/resume/skip)
    NS->>TTS: Control TTS Playback
    TTS-->>NS: Acknowledge Control
    NS-->>UI: Update UI State
```

## Choice Processing System

```mermaid
flowchart TD
    UserChoice([User Selects Choice]) --> ValidateChoice{Valid Choice?}
    
    ValidateChoice -->|No| ShowError[Show Error Message]
    ValidateChoice -->|Yes| RecordChoice[Record Choice in Progress]
    
    RecordChoice --> UpdateMoralProgress[Update Moral Value Progress]
    UpdateMoralProgress --> CheckConsequences{Choice Has Consequences?}
    
    CheckConsequences -->|Yes| ShowConsequence[Show Choice Consequence]
    CheckConsequences -->|No| DirectTransition[Direct Scene Transition]
    
    ShowConsequence --> PlayConsequenceAudio[Play Consequence Audio]
    PlayConsequenceAudio --> DirectTransition
    
    DirectTransition --> LoadingScreen[Show Loading Screen]
    LoadingScreen --> PreloadNextScene[Preload Next Scene]
    PreloadNextScene --> TransitionAnimation[Play Transition Animation]
    TransitionAnimation --> NextScene[Start Next Scene]
    
    NextScene --> UpdateProgress[Update Story Progress]
    UpdateProgress --> CheckAchievements[Check for Achievements]
    CheckAchievements --> ContinueStory[Continue Story Flow]
    
    style UserChoice fill:#e8f5e8
    style RecordChoice fill:#e3f2fd
    style ShowConsequence fill:#fff3e0
    style NextScene fill:#fce4ec
```
