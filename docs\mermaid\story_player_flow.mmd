flowchart TD
    %% Story Selection and Loading
    A[User Selects Story from Library] --> B[Navigate to /new_story/introduction/:storyId]
    B --> C[StoryIntroductionScreen]
    C --> D[Load Story via EnhancedStoryService]
    D --> E[Display Story Welcome Content]
    E --> F[Navigate to Character Introduction]
    
    %% Character Introduction Phase
    F --> G[CharacterIntroductionScreen]
    G --> H[Load Character Data]
    H --> I[Display Character Profiles]
    I --> J[Character Voice Narration]
    J --> K[Navigate to Story Player]
    
    %% Story Player Initialization
    K --> L[NewStoryPlayerScreen]
    L --> M[Initialize Services]
    M --> N[Load Story Settings]
    N --> O[Load User Progress]
    O --> P{First Time Playing?}
    P -->|Yes| Q[Start from First Scene - Rule 1]
    P -->|No| Q
    
    %% Scene Loading Process
    Q --> R[Load Scene Data]
    R --> S[Preload Scene Image]
    S --> T[Display Scene Image]
    T --> U[Wait 500ms - Rule 2]
    U --> V[Split Scene Text into Sentences]
    V --> W[Initialize Sentence Index = 0]
    
    %% Narration Process
    W --> X[Start Sentence Narration - Rule 3]
    X --> Y[Highlight Current Sentence - Rule 13]
    Y --> Z[TTS Narrate Current Sentence]
    Z --> AA[Word-Level Highlighting - Rule 4]
    AA --> BB[Sentence Completion]
    BB --> CC[1000ms Pause - Rule 5]
    CC --> DD{More Sentences?}
    DD -->|Yes| EE[Increment Sentence Index]
    EE --> Y
    DD -->|No| FF[Scene Narration Complete]
    
    %% Scene Completion Logic
    FF --> GG{Choices Available? - Rule 6}
    GG -->|Yes| HH[Show Choice Popup - Rule 8]
    GG -->|No| II{Auto/Manual Mode? - Rule 16}
    
    %% Choice Handling
    HH --> JJ[Narrate Choice Question - Rule 9]
    JJ --> KK[Narrate Each Option - Rule 9]
    KK --> LL[Wait for User Selection - Rule 10]
    LL --> MM[User Taps Choice]
    MM --> NN[Load Next Scene Based on Choice - Rule 11]
    NN --> R
    
    %% Auto/Manual Mode Handling
    II -->|Auto Mode| OO[Auto Advance to Next Scene - Rule 7]
    II -->|Manual Mode| PP[Show Manual Mode Indicator]
    PP --> QQ[Wait for User Input]
    QQ --> RR[User Taps Next Button]
    RR --> OO
    OO --> R
    
    %% Navigation Controls - Rule 12
    L --> SS[Control Interface]
    SS --> TT[Previous Sentence/Scene Button]
    SS --> UU[Play/Pause Narration Button]
    SS --> VV[Next Sentence/Scene Button]
    
    TT --> WW{Current Position?}
    WW -->|First Sentence| XX[Go to Previous Scene]
    WW -->|Not First| YY[Go to Previous Sentence]
    XX --> R
    YY --> Y
    
    UU --> ZZ{Narration State?}
    ZZ -->|Playing| AAA[Pause Narration]
    ZZ -->|Paused| BBB[Resume Narration]
    ZZ -->|Stopped| X
    
    VV --> CCC{Current Position?}
    CCC -->|Last Sentence| DDD{Choices Available?}
    CCC -->|Not Last| EEE[Go to Next Sentence]
    DDD -->|Yes| HH
    DDD -->|No| OO
    EEE --> Y
    
    %% Settings Integration - Rule 17
    L --> FFF[Load Child Profile Settings]
    FFF --> GGG[Apply Story Mode Preference]
    GGG --> HHH[Apply Font Size & Speed]
    HHH --> III[Apply Transparency Settings]
    
    %% Progress Tracking
    R --> JJJ[Track Scene Visit]
    MM --> KKK[Track Choice Selection]
    BB --> LLL[Track Sentence Progress]
    
    %% Error Handling
    D --> MMM{Loading Error?}
    MMM -->|Yes| NNN[Show Error Screen]
    MMM -->|No| E
    NNN --> OOO[Retry Button]
    OOO --> D
    
    %% Story Completion
    R --> PPP{Last Scene?}
    PPP -->|Yes| QQQ[Story Complete]
    PPP -->|No| Continue
    QQQ --> RRR[Show Completion Screen]
    RRR --> SSS[Award Rewards]
    SSS --> TTT[Save Final Progress]
    TTT --> UUU[Return to Home]
    
    %% Exit Flow
    SS --> VVV[Exit Button]
    VVV --> WWW[Navigate to Calm Exit]
    WWW --> XXX[CalmExitScreen]
    XXX --> UUU
    
    %% Style Definitions
    classDef userAction fill:#ff6b6b,stroke:#333,stroke-width:2px,color:#fff
    classDef systemProcess fill:#4ecdc4,stroke:#333,stroke-width:2px,color:#fff
    classDef narrationFlow fill:#feca57,stroke:#333,stroke-width:2px,color:#333
    classDef decision fill:#ff9ff3,stroke:#333,stroke-width:2px,color:#333
    classDef ruleCompliance fill:#96ceb4,stroke:#333,stroke-width:3px,color:#333
    classDef errorHandling fill:#ff7675,stroke:#333,stroke-width:2px,color:#fff
    
    %% Apply Styles
    class A,MM,RR,OOO userAction
    class B,C,D,G,H,L,M,N,R,S,T,FFF,JJJ,KKK,LLL systemProcess
    class X,Y,Z,AA,BB,JJ,KK narrationFlow
    class P,DD,GG,II,WW,ZZ,CCC,DDD,MMM,PPP decision
    class U,CC,HH,LL,NN,PP,QQ,OO ruleCompliance
    class NNN,XXX errorHandling
