# Prompt for AI Coding Assistant

## Context
The **Choice: Once Upon A Time** Flutter application is a children's storytelling app targeting ages 6-12 in India and the US. It features interactive stories with moral lessons and choice points. Stories are currently loaded from the app's assets using a multi-source loading system (bundled assets, local cache, or remote streaming). The story structure is defined in JSON files (`story.json`) with metadata and content organized into chapters and scenes, as outlined in `story_instruction.md`. The app uses Riverpod for state management and GoRouter for navigation. The current implementation lacks a rewards system and support for fetching compressed story files from Firebase Storage.

The task is to update the story integration to include a new story format with rewards, enable fetching stories from both assets and Firebase Storage (in compressed ZIP files), support offline playback, and handle missing assets gracefully with placeholders and Text-to-Speech (TTS) narration, all without modifying the homepage.

## Objectives
1. **Enhance Story Structure with Rewards:**
   - Add a `"rewards"` field to the story metadata to include completion rewards and moral choice rewards.
   - Update the app to parse and display these rewards to enhance user engagement.

2. **Enable Story Fetching from Multiple Sources:**
   - Fetch stories from app assets (e.g., `assets/stories/<story_id>/`).
   - Fetch compressed story files (ZIP format) from Firebase Storage, extract them, and store them locally.
   - Prioritize loading from local storage, then assets, then Firebase Storage.

3. **Support Offline Access:**
   - Cache downloaded stories locally for offline playback.
   - Ensure seamless playback of bundled and downloaded stories without internet.

4. **Handle Missing Assets Gracefully:**
   - Use placeholder images for missing visuals.
   - Fall back to TTS narration when audio files are unavailable.

5. **Maintain Homepage Integrity:**
   - Keep the homepage (`lib/features/story_library/presentation/screens/home_screen.dart`) unchanged in functionality and appearance.

## Instructions

### 1. Update Story Structure
- **Modify Metadata Parsing:**
  - Update the story model to parse the new `"rewards"` field in `story.json`, containing:
    - `"completion"`: A string (e.g., "Unity Badge") for completing the story.
    - `"moralChoices"`: A list of strings (e.g., ["Sharing Star", "Community Medal"]) for moral decisions.
  - Example: 
    ```json
    "rewards": {
      "completion": "Unity Badge",
      "moralChoices": ["Sharing Star", "Community Medal"]
    }
    ```
- **Update Story Player:**
  - Track user choices in choice-point scenes and award corresponding rewards from `"moralChoices"`.
  - On story completion, award the `"completion"` reward.
- **Display Rewards:**
  - Show rewards in a child-friendly UI (e.g., badges or stars) using a dialog or widget after choices and story completion.

### 2. Implement Firebase Storage Integration
- **Dependencies:**
  - Add `firebase_storage` for downloading files and `archive` for ZIP extraction to `pubspec.yaml`.
- **Download Process:**
  - Fetch compressed story files from Firebase Storage (e.g., `gs://your-app-bucket/stories/<story_id>.zip`).
  - Download the ZIP file to a temporary directory using `path_provider`.
- **Extraction:**
  - Use the `archive` package to extract the ZIP contents (e.g., `story.json`, images, audio).
  - Store extracted files in a local directory (e.g., `/stories/<story_id>/`) using `path_provider`.
- **Cleanup:**
  - Delete the temporary ZIP file after extraction.

### 3. Update Story Loading Logic
- **Loading Priority:**
  - Check local storage first (e.g., `/stories/<story_id>/story.json`).
  - If not found, check bundled assets (e.g., `assets/stories/<story_id>/story.json`) using `rootBundle`.
  - If not in assets, prompt the user to download from Firebase Storage.
- **Download Flow:**
  - On user confirmation, download and extract the story from Firebase Storage.
  - Save it to local storage for future offline access.
- **Error Handling:**
  - Show user-friendly messages for network failures (e.g., "No internet connection") or missing stories.

### 4. Handle Missing Assets
- **Images:**
  - If an image (`imageUrl`) is missing or fails to load, display a placeholder (e.g., `assets/placeholder.jpg`).
- **Audio:**
  - If an audio file (`audioUrl`) is missing, use TTS to narrate the scene’s `"text"` field.
- **Robustness:**
  - Ensure the app continues functioning even if assets are unavailable.

### 5. Integrate TTS for Narration
- **Dependency:**
  - Add `flutter_tts` to `pubspec.yaml`.
- **Implementation:**
  - When audio is missing, use `flutter_tts` to narrate the `"text"` from the scene JSON.
  - Adjust TTS voice based on `"emotionCue"` (e.g., "happy" increases pitch, "sad" lowers speed).
- **Synchronization:**
  - Sync TTS narration with text display and scene transitions for a smooth experience.

### 6. Ensure Offline Functionality
- **Playback:**
  - Enable playback of stories from local storage and bundled assets without internet.
- **User Feedback:**
  - If a story is not available offline, show a message like "Connect to download this story."
- **Seamless Transitions:**
  - Handle online-to-offline transitions without interrupting the user experience.

### 7. Maintain Homepage Integrity
- **Constraint:**
  - Do not modify `lib/features/story_library/presentation/screens/home_screen.dart`.
- **Focus:**
  - Confine changes to story fetching, parsing, and presentation logic (e.g., story player, providers).

## Technical Details
- **Architecture:**
  - Use Riverpod for state management (e.g., providers for story data, rewards).
  - Use GoRouter for navigation between screens.
- **File Storage:**
  - Assets: `assets/stories/<story_id>/` (bundled with the app).
  - Local: `/stories/<story_id>/` (downloaded and extracted).
- **Dependencies:**
  - `path_provider`: For local file storage.
  - `firebase_storage`: For downloading from Firebase.
  - `archive`: For ZIP extraction.
  - `flutter_tts`: For TTS narration.
- **Asset Access:**
  - Use `rootBundle` for bundled assets.
  - Use Dart’s `File` class for local storage.

## Example Updated `story.json`
```json
{
  "id": "story1",
  "title": { "en-US": "The Lantern of Unity" },
  "description": { "en-US": "A story about sharing and community." },
  "targetMoralValue": "Sharing and Community",
  "targetAgeSubSegment": "5-7",
  "estimatedDurationMinutes": 15,
  "coverImageUrl": "assets/images/story_covers/story1.jpg",
  "isNew": false,
  "isLocked": false,
  "hasProgress": false,
  "version": "1.0.0",
  "rewards": {
    "completion": "Unity Badge",
    "moralChoices": ["Sharing Star", "Community Medal"]
  },
  "chapters": [
    {
      "chapterId": "chapter_1",
      "title": "The Beginning",
      "scenes": [
        {
          "sceneId": "scene_1",
          "type": "narrative",
          "content": {
            "text": "Once upon a time...",
            "imageUrl": "assets/images/story1/scene_1.jpg",
            "audioUrl": "assets/audio/story1/scene_1.mp3",
            "emotionCue": "gentle"
          }
        }
      ]
    }
  ]
}
```

## Expected Outcome
- Stories include a rewards system to boost engagement.
- Users can fetch stories from assets or Firebase Storage, with offline access via local storage.
- Missing assets are handled with placeholders and TTS, ensuring a robust experience.
- The homepage remains unchanged, preserving the existing user interface.

## Constraints
- Do not alter the homepage’s functionality or appearance.
- Ensure compatibility with existing dependencies (Riverpod, GoRouter) and architecture.
- Maintain support for the multi-source loading system outlined in `story_instruction.md`.