# Data Models and Relationships

## Core Story Models

```mermaid
erDiagram
    EnhancedStoryModel {
        string storyId
        string ageGroup
        string difficulty
        string title
        string moral
        string coverImage
        StorySetupModel setup
        NarratorProfileModel narratorProfile
        List characters
        List scenes
        PostStoryModel postStory
    }
    
    EnhancedSceneModel {
        string id
        string text
        string speaker
        string emotion
        string image
        int pauseDuration
        List choices
        string next
    }
    
    ChoiceOptionModel {
        string option
        string visual
        string next
    }
    
    StorySetupModel {
        string setting
        string tone
        string context
    }
    
    NarratorProfileModel {
        string name
        VoiceModel voice
    }
    
    VoiceModel {
        double pitch
        double rate
        double volume
    }
    
    CharacterModel {
        string name
        string description
        string imageUrl
        VoiceModel voice
    }
    
    EnhancedStoryModel ||--|| StorySetupModel : contains
    EnhancedStoryModel ||--|| NarratorProfileModel : has
    EnhancedStoryModel ||--o{ CharacterModel : includes
    EnhancedStoryModel ||--o{ EnhancedSceneModel : contains
    EnhancedSceneModel ||--o{ ChoiceOptionModel : has
    NarratorProfileModel ||--|| VoiceModel : uses
    CharacterModel ||--|| VoiceModel : has
```

## Progress Tracking Models

```mermaid
erDiagram
    ChildProgressModel {
        string profileId
        string storyId
        DateTime lastPlayedAt
        DateTime createdAt
        DateTime updatedAt
        bool isCompleted
        string completedOutcome
        int totalPlayTime
        int completionCount
        List visitedScenes
        Map choicesMade
        Map scenePlayTime
        List moralValues
        List vocabularyLearned
    }
    
    MoralValueProgress {
        string moralValue
        int exposureCount
        DateTime firstEncounter
        DateTime lastReinforcement
        double comprehensionScore
        List relatedStories
    }
    
    VocabularyProgress {
        string word
        string definition
        string context
        DateTime learnedAt
        int encounterCount
        bool isMastered
    }
    
    UserProfileModel {
        string profileId
        string displayName
        int age
        string preferredLanguage
        DateTime createdAt
        DateTime lastActiveAt
        Map preferences
        List completedStories
        Map achievements
    }
    
    ChildProgressModel ||--o{ MoralValueProgress : tracks
    ChildProgressModel ||--o{ VocabularyProgress : includes
    UserProfileModel ||--o{ ChildProgressModel : has
```

## Narration Models

```mermaid
erDiagram
    NarrationState {
        NarrationStatus status
        string currentText
        int currentWordIndex
        int currentSentenceIndex
        double progress
        bool autoProgress
        NarrationProgress progressData
    }
    
    NarrationProgress {
        string storyId
        string sceneId
        int currentWordIndex
        int currentSentenceIndex
        int totalWords
        int totalSentences
        int completedWords
        int completedSentences
        DateTime lastUpdated
    }
    
    NarrationConfig {
        double speechRate
        double speechPitch
        double speechVolume
        int sentencePauseMs
        int wordPauseMs
        bool autoProgress
        bool enableEmotions
        string voiceId
    }
    
    WordHighlight {
        int wordIndex
        int sentenceIndex
        string word
        DateTime timestamp
        bool isActive
    }
    
    NarrationState ||--|| NarrationProgress : contains
    NarrationState ||--o{ WordHighlight : tracks
```

## Settings and Configuration Models

```mermaid
erDiagram
    StorySettings {
        double fontSize
        double subtitleTransparency
        double narrationSpeed
        bool isAutoplayEnabled
    }
    
    AppSettings {
        string language
        string theme
        bool soundEffectsEnabled
        bool voiceGuidanceEnabled
        double masterVolume
        bool parentalControlsEnabled
    }
    
    TTSConfig {
        double rate
        double pitch
        double volume
        string language
        string voice
        bool enableSSML
    }
    
    EmotionParameters {
        double pitchMultiplier
        double rateMultiplier
        double volumeMultiplier
        int pauseMultiplier
    }
    
    StorySettings ||--|| TTSConfig : configures
    TTSConfig ||--o{ EmotionParameters : uses
```
