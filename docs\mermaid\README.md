# Mermaid Documentation for Choice: Once Upon A Time

## Overview
This directory contains comprehensive mermaid diagrams and documentation for the Flutter storytelling app "Choice: Once Upon A Time". These diagrams provide visual representations of the app's architecture, user flows, data models, and system interactions.

## Quick Start Guide for New Team Members

### 1. Project Understanding
Start with these diagrams in order:
1. **`01_project_overview.md`** - High-level system architecture
2. **`02_user_flow.md`** - Complete user journey mapping
3. **`03_feature_architecture.md`** - Feature-based architecture breakdown
4. **`04_data_models.md`** - Data structure relationships
5. **`05_services_architecture.md`** - Service layer interactions

### 2. Development Focus Areas
- **Story System**: See `06_story_system.md` for story loading, narration, and playback
- **Navigation**: See `07_navigation_flow.md` for screen transitions and routing
- **Parent Zone**: See `08_parent_zone.md` for parental controls and progress tracking
- **TTS & Audio**: See `09_audio_system.md` for text-to-speech and sound management

### 3. Technical Implementation
- **State Management**: Riverpod-based architecture
- **Navigation**: GoRouter with feature-first structure
- **Storage**: Asset-based stories with Firebase integration
- **Audio**: Enhanced TTS with emotion-based narration

## File Structure
```
docs/mermaid/
├── README.md                    # This guide
├── 01_project_overview.md       # High-level architecture
├── 02_user_flow.md             # User journey flows
├── 03_feature_architecture.md   # Feature breakdown
├── 04_data_models.md           # Data relationships
├── 05_services_architecture.md  # Service interactions
├── 06_story_system.md          # Story loading & playback
├── 07_navigation_flow.md       # Screen navigation
├── 08_parent_zone.md           # Parent features
├── 09_audio_system.md          # TTS & audio system
├── main_dart_dependencies.md   # Main.dart dependency analysis
├── app_state_diagram.mmd       # Complete application state flow
├── component_architecture.mmd  # Component relationships
└── story_player_flow.mmd       # Story player detailed flow
```

## 🆕 New Main.dart Analysis Diagrams

### Main Dependencies Analysis
- **main_dart_dependencies.md** - Complete list of all files and dependencies connected to main.dart
- **app_state_diagram.mmd** - Comprehensive state flow diagram showing complete application lifecycle
- **component_architecture.mmd** - High-level component relationships and data flow
- **story_player_flow.mmd** - Detailed flow diagram for story player with 17-rule compliance

### Key Features of New Diagrams

#### Application State Diagram (`app_state_diagram.mmd`)
- Entry point initialization sequence
- Navigation flow between screens
- Story player state machine with 17-rule compliance
- Parent zone navigation
- Service integration points
- Error handling flows

#### Component Architecture (`component_architecture.mmd`)
- Application layers (Entry, Infrastructure, State Management, Features)
- Service dependencies
- Data source connections
- Global component integration

#### Story Player Flow (`story_player_flow.mmd`)
- 17-rule compliance visualization
- Scene loading and narration process
- Choice handling and navigation
- User controls and settings integration
- Error handling and recovery

## 🎯 Story Player Rule Compliance

The new diagrams show how the NewStoryPlayerScreen implements all 17 rules:

1. **Rule 1**: Always start with first scene
2. **Rule 2**: 500ms wait after image loading
3. **Rule 3**: Sentence-by-sentence narration
4. **Rule 4**: Word-level highlighting during narration
5. **Rule 5**: 1000ms pause between sentences
6. **Rule 6**: Check choices after last sentence
7. **Rule 7**: Auto-advance if no choices
8. **Rule 8**: Show popup if choices available
9. **Rule 9**: Narrate popup questions and options
10. **Rule 10**: Handle user choice selection
11. **Rule 11**: Load next scene based on choice
12. **Rule 12**: Navigation controls (previous/play-pause/next)
13. **Rule 13**: Single sentence subtitle display
14. **Rule 14**: Balanced timing between elements
15. **Rule 15**: No loading animations
16. **Rule 16**: Default manual mode
17. **Rule 17**: Settings persistence via child profiles

## How to Use These Diagrams

### For New Developers
1. Read the project overview to understand the big picture
2. Study the user flow to understand the user experience
3. Examine the feature architecture to see how code is organized
4. Review data models to understand the information structure
5. Look at services to understand business logic

### For Product Managers
- Focus on user flow diagrams for UX understanding
- Review feature architecture for scope planning
- Check parent zone diagrams for parental control features

### For QA Engineers
- Use user flow diagrams for test case planning
- Reference navigation flows for testing scenarios
- Study story system for content testing

## Key Technologies
- **Flutter**: Cross-platform mobile development
- **Firebase**: Backend-as-a-Service for data and authentication
- **Riverpod**: State management
- **GoRouter**: Navigation and routing
- **flutter_tts**: Text-to-speech functionality
- **Asset-based Stories**: Local story content with remote capabilities

## Development Principles
- **Feature-First Architecture**: Code organized by features, not layers
- **Offline-First**: Stories work without internet connection
- **Child-Friendly**: Designed for ages 6-12 with parental controls
- **Responsive Design**: Works on phones, tablets, and web
- **Accessibility**: Screen reader support and large touch targets

## Getting Started with Development
1. Clone the repository
2. Run `flutter pub get` to install dependencies
3. Study these mermaid diagrams to understand the architecture
4. Start with the feature you're assigned to work on
5. Follow the coding guidelines in the main documentation

## Detailed Diagram Descriptions

### 01_project_overview.md
- **System Architecture**: Shows the high-level relationship between Flutter app, services, and external systems
- **Technology Stack**: Visual representation of all technologies used in the project
- **Application Layers**: Clean architecture layers and their interactions

### 02_user_flow.md
- **Complete User Journey**: End-to-end user experience from app launch to story completion
- **Story Playback Flow**: Detailed flow of story narration and interaction
- **Navigation Hierarchy**: Screen relationships and navigation patterns

### 03_feature_architecture.md
- **Feature-First Structure**: How code is organized by features rather than technical layers
- **Individual Feature Breakdowns**: Internal structure of major features like story library and story player

### 04_data_models.md
- **Core Story Models**: Data structure for stories, scenes, and choices
- **Progress Tracking Models**: How child progress and learning is tracked
- **Narration Models**: Data structures for TTS and narration state management

### 05_services_architecture.md
- **Core Services Overview**: All major services and their relationships
- **TTS and Narration Architecture**: Detailed audio and narration service hierarchy
- **Story Loading Architecture**: How stories are loaded from assets and cached

### 06_story_system.md
- **Story Loading and Playback Flow**: Complete story experience from selection to completion
- **Story Data Structure**: Asset organization and content model
- **Choice Processing System**: How user choices are handled and processed

### 07_navigation_flow.md
- **GoRouter Configuration**: All application routes and their hierarchy
- **Screen Hierarchy**: Navigation levels and back button behavior
- **Story Player Navigation States**: State management within story playback

### 08_parent_zone.md
- **Parent Zone Features**: Authentication, dashboard, and parental controls
- **Progress Tracking System**: How child progress is collected, analyzed, and visualized
- **Profile Management**: Child profile creation and management system

### 09_audio_system.md
- **TTS Service Hierarchy**: Text-to-speech service architecture
- **Emotion-Based TTS**: How emotions are mapped to voice parameters
- **Voice Guidance System**: Screen narration and guidance features

## Development Workflow

### For New Features
1. Study the relevant feature architecture diagram
2. Review the user flow to understand the user experience
3. Check the services architecture to understand dependencies
4. Implement following the feature-first structure
5. Update these diagrams if the architecture changes

### For Bug Fixes
1. Use the user flow diagrams to reproduce the issue
2. Check the relevant service architecture to understand the code flow
3. Review the data models to understand the data structures involved
4. Fix the issue and test the complete user flow

### For Code Reviews
1. Ensure new code follows the architectural patterns shown in these diagrams
2. Verify that feature organization matches the feature architecture
3. Check that service dependencies align with the services architecture

## Support
For questions about the architecture or implementation:
- Check the main documentation in `/docs/`
- Review the code in `/lib/` following the feature-first structure
- Refer to these mermaid diagrams for visual understanding
- Use the navigation flow diagrams for testing scenarios
- Reference the data model diagrams for database and API design
