# Project Overview - High-Level Architecture

## System Architecture

```mermaid
graph TD
    User[Child/Parent User] -->|Interacts with| FlutterApp[Flutter Mobile App]
    
    subgraph FlutterApp["Flutter Application"]
        direction TB
        UI[UI Layer - Widgets & Screens]
        StateMgmt[State Management - Riverpod]
        Features[Feature Modules]
        Core[Core Services]
        Models[Data Models]
        
        UI --> StateMgmt
        StateMgmt --> Features
        Features --> Core
        Features --> Models
    end
    
    subgraph Features["Feature Modules"]
        direction LR
        AppInit[App Initialization]
        StoryLibrary[Story Library]
        StoryPlayer[Story Player]
        ParentZone[Parent Zone]
        Auth[Authentication]
    end
    
    subgraph Core["Core Services"]
        direction LR
        TTS[TTS Service]
        Storage[Storage Service]
        Audio[Audio Service]
        Navigation[Navigation Service]
    end
    
    FlutterApp -->|Local Storage| DeviceStorage[Device Storage]
    FlutterApp -->|Asset Loading| AssetBundle[App Assets]
    FlutterApp -->|TTS Requests| NativeTTS[Native TTS Engine]
    FlutterApp -->|Future: API Calls| Firebase[Firebase BaaS]
    
    style FlutterApp fill:#e1f5fe
    style Features fill:#f3e5f5
    style Core fill:#e8f5e8
```

## Technology Stack

```mermaid
graph LR
    subgraph Frontend["Frontend Technologies"]
        Flutter[Flutter Framework]
        Dart[Dart Language]
        Material[Material Design 3]
    end
    
    subgraph StateManagement["State Management"]
        Riverpod[Riverpod]
        GoRouter[GoRouter Navigation]
    end
    
    subgraph Audio["Audio Technologies"]
        FlutterTTS[flutter_tts]
        NativeAudio[Native Audio APIs]
        SSML[SSML Support]
    end
    
    subgraph Storage["Storage Solutions"]
        Assets[Asset Bundle]
        SharedPrefs[SharedPreferences]
        FileSystem[File System]
        FutureFirebase[Future: Firebase]
    end
    
    subgraph Platform["Platform Support"]
        iOS[iOS]
        Android[Android]
        Web[Web - Chrome]
    end
    
    Flutter --> Riverpod
    Flutter --> GoRouter
    Flutter --> FlutterTTS
    Flutter --> Assets
    Flutter --> iOS
    Flutter --> Android
    Flutter --> Web
    
    style Frontend fill:#e3f2fd
    style StateManagement fill:#f1f8e9
    style Audio fill:#fff3e0
    style Storage fill:#fce4ec
    style Platform fill:#f3e5f5
```

## Application Layers

```mermaid
graph TD
    subgraph PresentationLayer["Presentation Layer"]
        Screens[Screens]
        Widgets[Widgets]
        Providers[Riverpod Providers]
    end
    
    subgraph DomainLayer["Domain Layer"]
        UseCases[Use Cases]
        Entities[Entities/Models]
        Interfaces[Service Interfaces]
    end
    
    subgraph DataLayer["Data Layer"]
        Repositories[Repositories]
        DataSources[Data Sources]
        LocalStorage[Local Storage]
    end
    
    subgraph CoreLayer["Core Layer"]
        Services[Core Services]
        Utils[Utilities]
        Constants[Constants]
    end
    
    PresentationLayer --> DomainLayer
    DomainLayer --> DataLayer
    PresentationLayer --> CoreLayer
    DataLayer --> CoreLayer
    
    style PresentationLayer fill:#e8eaf6
    style DomainLayer fill:#e0f2f1
    style DataLayer fill:#fff8e1
    style CoreLayer fill:#fce4ec
```
