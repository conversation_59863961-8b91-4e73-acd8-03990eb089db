# Audio System Architecture

## TTS and Audio Service Hierarchy

```mermaid
graph TD
    subgraph UILayer["UI Layer"]
        StoryPlayer[Story Player Screen]
        NarrationControls[Narration Controls Widget]
        VoiceGuideWidget[Voice Guide Widget]
    end
    
    subgraph ServiceLayer["Service Layer"]
        EnhancedNarrationService[Enhanced Story Narration Service]
        VoiceGuidanceManager[Voice Guidance Manager]
        SoundEffectPlayer[Sound Effect Player Service]
        StorySettingsService[Story Settings Service]
    end
    
    subgraph TTSLayer["TTS Layer"]
        EnhancedTTSService[Enhanced Narration TTS Service]
        UnifiedTTSService[Unified TTS Service]
        EmotionCueMapper[Emotion Cue Mapper Service]
    end
    
    subgraph PlatformLayer["Platform Layer"]
        FlutterTTSPlugin[flutter_tts Plugin]
        NativeTTSEngine[Native TTS Engine]
        AudioSystem[Platform Audio System]
    end
    
    UILayer --> ServiceLayer
    ServiceLayer --> TTSLayer
    TTSLayer --> PlatformLayer
    
    style UILayer fill:#e8eaf6
    style ServiceLayer fill:#e0f2f1
    style TTSLayer fill:#fff8e1
    style PlatformLayer fill:#fce4ec
```

## TTS Service Architecture

```mermaid
graph TD
    subgraph Interfaces["Service Interfaces"]
        ITTSService[ITTSService]
        IEnhancedTTSService[IEnhancedNarrationTTSService]
        IStoryNarrationService[IStoryNarrationService]
    end
    
    subgraph Implementations["Service Implementations"]
        UnifiedTTS[Unified TTS Service]
        EnhancedTTS[Enhanced Narration TTS Service]
        StoryNarration[Enhanced Story Narration Service]
    end
    
    subgraph Configuration["Configuration Services"]
        TTSConfig[TTS Configuration]
        EmotionMapper[Emotion Cue Mapper]
        VoiceProfiles[Voice Profiles]
        SettingsService[Settings Service]
    end
    
    subgraph Platform["Platform Integration"]
        FlutterTTS[flutter_tts]
        iOSTTS[iOS Speech Synthesis]
        AndroidTTS[Android Text-to-Speech]
        WebSpeechAPI[Web Speech API]
    end
    
    Interfaces --> Implementations
    Implementations --> Configuration
    Implementations --> Platform
    
    style Interfaces fill:#e8eaf6
    style Implementations fill:#e0f2f1
    style Configuration fill:#fff8e1
    style Platform fill:#fce4ec
```

##  Cache-->>VGM: Cache Status
        
        alt Guide Not Cached
            VGM->>VGM: Generate Voice Guide Text
            VGM->>Cache: Store Generated Guide
        end
        
        VGM->>TTS: Play Voice Guide
        TTS-->>VGM: Playback Started
        VGM-->>Screen: Voice Guide Playing
        
        TTS-->>VGM: Playback Complete
        VGM-->>Screen: Voice Guide Complete
    else Voice Guidance Disabled
        VGM-->>Screen: Voice Guide Skipped
    end
```
Emotion-Based TTS System

```mermaid
flowchart TD
    SceneText[Scene Text with Emotion] --> EmotionDetection[Emotion Detection]
    EmotionDetection --> EmotionMapper[Emotion Cue Mapper]
    
    EmotionMapper --> EmotionParams{Emotion Parameters}
    EmotionParams --> Happy[Happy: Higher pitch, faster rate]
    EmotionParams --> Sad[Sad: Lower pitch, slower rate]
    EmotionParams --> Excited[Excited: Higher pitch, faster rate, higher volume]
    EmotionParams --> Calm[Calm: Normal pitch, slower rate, softer volume]
    EmotionParams --> Mysterious[Mysterious: Lower pitch, slower rate, pauses]
    EmotionParams --> Neutral[Neutral: Default parameters]
    
    Happy --> TTSConfig[TTS Configuration]
    Sad --> TTSConfig
    Excited --> TTSConfig
    Calm --> TTSConfig
    Mysterious --> TTSConfig
    Neutral --> TTSConfig
    
    TTSConfig --> ApplyParameters[Apply Parameters to TTS]
    ApplyParameters --> SpeakText[Speak Text with Emotion]
    SpeakText --> WordHighlighting[Word-by-Word Highlighting]
    WordHighlighting --> NarrationComplete[Narration Complete]
    
    style SceneText fill:#e8f5e8
    style EmotionParams fill:#e3f2fd
    style TTSConfig fill:#fff3e0
    style WordHighlighting fill:#fce4ec
```

## Voice Guidance System

```mermaid
sequenceDiagram
    participant Screen as Screen Widget
    participant VGM as Voice Guidance Manager
    participant TTS as TTS Service
    participant Settings as Settings Service
    participant Cache as Voice Cache
    
    Screen->>VGM: Request Voice Guide
    VGM->>Settings: Check Voice Guidance Enabled
    Settings-->>VGM: Voice Guidance Status
    
    alt Voice Guidance Enabled
        VGM->>Cache: Check for Cached Guide
       
## Audio State Management

```mermaid
stateDiagram-v2
    [*] --> Idle
    
    Idle --> Initializing : Initialize TTS
    Initializing --> Ready : TTS Ready
    Initializing --> Error : TTS Failed
    
    Ready --> Loading : Start Narration
    Loading --> Speaking : Text Loaded
    Loading --> Error : Load Failed
    
    Speaking --> Paused : Pause Requested
    Speaking --> Stopped : Stop Requested
    Speaking --> Complete : Text Finished
    
    Paused --> Speaking : Resume Requested
    Paused --> Stopped : Stop Requested
    
    Complete --> Ready : Ready for Next
    Stopped --> Ready : Reset Complete
    Error --> Ready : Retry/Reset
    
    %% Voice Guidance States
    Ready --> VoiceGuide : Play Voice Guide
    VoiceGuide --> Ready : Guide Complete
    VoiceGuide --> Error : Guide Failed
```

## Audio Configuration and Settings

```mermaid
graph TD
    subgraph UserSettings["User Settings"]
        NarrationSpeed[Narration Speed: 0.3-2.0x]
        Volume[Volume: 0-100%]
        VoiceSelection[Voice Selection]
        EmotionEnabled[Emotion Modulation]
        VoiceGuidanceEnabled[Voice Guidance]
    end
    
    subgraph TTSParameters["TTS Parameters"]
        SpeechRate[Speech Rate]
        SpeechPitch[Speech Pitch]
        SpeechVolume[Speech Volume]
        Language[Language Code]
        VoiceId[Voice Identifier]
    end
    
    subgraph EmotionModulation["Emotion Modulation"]
        PitchMultiplier[Pitch Multiplier]
        RateMultiplier[Rate Multiplier]
        VolumeMultiplier[Volume Multiplier]
        PauseMultiplier[Pause Multiplier]
    end
    
    subgraph AudioEffects["Audio Effects"]
        BackgroundMusic[Background Music]
        SoundEffects[Sound Effects]
        AmbientSounds[Ambient Sounds]
        TransitionSounds[Transition Sounds]
    end
    
    UserSettings --> TTSParameters
    TTSParameters --> EmotionModulation
    UserSettings --> AudioEffects
    
    style UserSettings fill:#e8eaf6
    style TTSParameters fill:#e0f2f1
    style EmotionModulation fill:#fff8e1
    style AudioEffects fill:#fce4ec
```
