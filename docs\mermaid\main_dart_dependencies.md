# Main.dart Dependencies Analysis

## Direct Dependencies from main.dart

### Core Flutter/Dart Packages
- `package:flutter/material.dart` - Flutter UI framework
- `package:flutter_riverpod/flutter_riverpod.dart` - State management
- `package:flutter_dotenv/flutter_dotenv.dart` - Environment variables
- `package:firebase_core/firebase_core.dart` - Firebase initialization

### Application Files
- `package:choice_once_upon_a_time/app/app_widget.dart` - Main app widget
- `package:choice_once_upon_a_time/core/services/accessibility_service.dart` - Accessibility features
- `package:choice_once_upon_a_time/core/utils/app_logger.dart` - Logging utility
- `package:choice_once_upon_a_time/core/services/story_rewards_service.dart` - Story rewards system
- `firebase_options.dart` - Firebase configuration

## Dependencies from app_widget.dart

### Direct Dependencies
- `package:flutter/material.dart` - Flutter UI framework
- `package:flutter_riverpod/flutter_riverpod.dart` - State management
- `package:go_router/go_router.dart` - Navigation routing
- `package:choice_once_upon_a_time/app/routing/app_router.dart` - App routing configuration
- `package:choice_once_upon_a_time/app/theme/app_theme.dart` - App theme definitions
- `package:choice_once_upon_a_time/widgets/global_narrator_controller.dart` - Global narrator state
- `package:choice_once_upon_a_time/widgets/global_narrator_widget.dart` - Global narrator UI
- `package:choice_once_upon_a_time/core/utils/app_logger.dart` - Logging utility

## Dependencies from app_router.dart

### Screen Dependencies (All Routes)
- `package:choice_once_upon_a_time/features/app_init/presentation/screens/launch_screen.dart`
- `package:choice_once_upon_a_time/features/app_init/presentation/screens/ftue_screen.dart`
- `package:choice_once_upon_a_time/features/app_init/presentation/screens/profile_selection_screen.dart`
- `package:choice_once_upon_a_time/features/app_init/presentation/screens/welcome_screen.dart`
- `package:choice_once_upon_a_time/features/story_library/presentation/screens/home_screen.dart`
- `package:choice_once_upon_a_time/features/story_library/presentation/screens/story_library_screen.dart`
- `package:choice_once_upon_a_time/features/story_player/presentation/screens/story_intro_screen.dart`
- `package:choice_once_upon_a_time/features/story_player/presentation/screens/loading_screen.dart`
- `package:choice_once_upon_a_time/features/story_player/presentation/screens/calm_exit_screen.dart`
- `package:choice_once_upon_a_time/features/story_player/presentation/screens/continue_story_screen.dart`
- `package:choice_once_upon_a_time/features/story_player/presentation/screens/enhanced_story_player_screen.dart`
- `package:choice_once_upon_a_time/features/story_player/presentation/screens/story_introduction_screen.dart`
- `package:choice_once_upon_a_time/features/story_player/presentation/screens/character_introduction_screen.dart`
- `package:choice_once_upon_a_time/features/story_player/presentation/screens/new_story_player_screen.dart`
- `package:choice_once_upon_a_time/features/auth/presentation/screens/parental_gate_screen.dart`
- `package:choice_once_upon_a_time/features/auth/presentation/screens/parent_auth_screen.dart`
- `package:choice_once_upon_a_time/features/parent_zone/presentation/screens/parent_zone_dashboard_screen.dart`
- `package:choice_once_upon_a_time/features/parent_zone/presentation/screens/sound_settings_screen.dart`
- `package:choice_once_upon_a_time/features/parent_zone/presentation/screens/user_profiles_screen.dart`
- `package:choice_once_upon_a_time/features/parent_zone/presentation/screens/progress_tracking_screen.dart`
- `package:choice_once_upon_a_time/features/parent_zone/presentation/screens/about_stories_screen.dart`
- `package:choice_once_upon_a_time/features/parent_zone/presentation/screens/manage_downloads_screen.dart`
- `package:choice_once_upon_a_time/features/parent_zone/presentation/screens/help_support_screen.dart`
- `package:choice_once_upon_a_time/features/parent_zone/presentation/screens/narrator_creation_screen.dart`
- `package:choice_once_upon_a_time/features/rewards/presentation/screens/rewards_screen.dart`
- `package:choice_once_upon_a_time/features/ai_stories/presentation/screens/ai_story_generation_screen.dart`
- `package:choice_once_upon_a_time/features/testing/presentation/screens/fallback_asset_test_screen.dart`
- `package:choice_once_upon_a_time/features/testing/presentation/screens/timing_test_screen.dart`

## Core Services Dependencies

### Accessibility Service
- `dart:async` - Async programming
- `package:flutter/services.dart` - Platform services
- `package:shared_preferences/shared_preferences.dart` - Local storage
- `package:logger/logger.dart` - Logging

### Story Rewards Service
- Story progress tracking
- Achievement system
- Reward calculations

### App Logger
- Centralized logging system
- Debug and error tracking

## Theme Dependencies

### App Theme
- `package:flutter/material.dart` - Material design components
- Color definitions and styling
- Typography and component themes

## Global Widgets

### Global Narrator Controller
- State management for narrator
- Route-based narration triggers

### Global Narrator Widget
- UI component for global narrator
- Audio playback controls

## Key Architecture Patterns

### State Management
- **Riverpod**: Primary state management solution
- **Provider Scope**: Global state container
- **Consumer Widgets**: State-aware UI components

### Navigation
- **GoRouter**: Declarative routing
- **Route Guards**: Authentication and access control
- **Deep Linking**: URL-based navigation

### Service Layer
- **Singleton Services**: Core app services
- **Dependency Injection**: Service registration
- **Initialization**: Async service setup

### Feature Modules
- **Feature-First Architecture**: Organized by functionality
- **Presentation Layer**: Screens and widgets
- **Data Layer**: Services and repositories
- **Domain Layer**: Business logic and models

## Initialization Flow

1. **main()** - Entry point
2. **WidgetsFlutterBinding.ensureInitialized()** - Flutter initialization
3. **dotenv.load()** - Environment variables
4. **Firebase.initializeApp()** - Firebase setup
5. **AccessibilityService.initialize()** - Accessibility features
6. **StoryRewardsService.initialize()** - Rewards system
7. **runApp(ProviderScope(child: AppWidget()))** - App launch

## Navigation Flow

1. **Launch Screen** (`/launch`) - Initial loading
2. **FTUE Screen** (`/ftue`) - First-time user experience
3. **Welcome Screen** (`/welcome`) - Welcome message
4. **Profile Selection** (`/profile_selection`) - User profile setup
5. **Home Screen** (`/home`) - Main dashboard
6. **Story Library** (`/story_library`) - Story browsing
7. **Story Player** (`/new_story/play/:storyId`) - Story playback
8. **Parent Zone** (`/parent_zone`) - Parent controls

## Service Integration

### Core Services
- **AccessibilityService**: UI accessibility features
- **StoryRewardsService**: Achievement and progress tracking
- **AppLogger**: Centralized logging and debugging

### Feature Services
- **Story Services**: Story loading and management
- **Audio Services**: TTS and sound effects
- **Progress Services**: User progress tracking
- **Settings Services**: User preferences and configuration

This dependency analysis shows the complete connection tree starting from main.dart and extending through all major application components.
