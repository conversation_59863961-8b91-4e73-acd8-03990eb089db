// Mocks generated by Mocki<PERSON> 5.4.4 from annotations
// in choice_once_upon_a_time/test/features/story_library/providers/enhanced_story_library_provider_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:choice_once_upon_a_time/core/services/new_story_service.dart'
    as _i4;
import 'package:choice_once_upon_a_time/core/services/story_cleanup_service.dart'
    as _i3;
import 'package:choice_once_upon_a_time/core/services/story_download_manager.dart'
    as _i10;
import 'package:choice_once_upon_a_time/features/story_library/data/story_repository.dart'
    as _i9;
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart'
    as _i7;
import 'package:choice_once_upon_a_time/models/firebase_story_metadata.dart'
    as _i11;
import 'package:choice_once_upon_a_time/models/story_metadata_model.dart'
    as _i6;
import 'package:choice_once_upon_a_time/models/story_model.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i8;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeStoryModel_0 extends _i1.SmartFake implements _i2.StoryModel {
  _FakeStoryModel_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeCleanupResult_1 extends _i1.SmartFake implements _i3.CleanupResult {
  _FakeCleanupResult_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [NewStoryService].
///
/// See the documentation for Mockito's code generation for more information.
class MockNewStoryService extends _i1.Mock implements _i4.NewStoryService {
  MockNewStoryService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<List<_i6.StoryMetadataModel>> getAllStoryMetadata() =>
      (super.noSuchMethod(
        Invocation.method(
          #getAllStoryMetadata,
          [],
        ),
        returnValue: _i5.Future<List<_i6.StoryMetadataModel>>.value(
            <_i6.StoryMetadataModel>[]),
      ) as _i5.Future<List<_i6.StoryMetadataModel>>);

  @override
  _i5.Future<_i7.EnhancedStoryModel?> loadStory(String? storyId) =>
      (super.noSuchMethod(
        Invocation.method(
          #loadStory,
          [storyId],
        ),
        returnValue: _i5.Future<_i7.EnhancedStoryModel?>.value(),
      ) as _i5.Future<_i7.EnhancedStoryModel?>);

  @override
  _i5.Future<bool> isStoryAvailableLocally(String? storyId) =>
      (super.noSuchMethod(
        Invocation.method(
          #isStoryAvailableLocally,
          [storyId],
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<String> getStoryStatus(String? storyId) => (super.noSuchMethod(
        Invocation.method(
          #getStoryStatus,
          [storyId],
        ),
        returnValue: _i5.Future<String>.value(_i8.dummyValue<String>(
          this,
          Invocation.method(
            #getStoryStatus,
            [storyId],
          ),
        )),
      ) as _i5.Future<String>);

  @override
  _i5.Future<String> getSceneImageWithFallback(
    String? storyId,
    String? imageName,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #getSceneImageWithFallback,
          [
            storyId,
            imageName,
          ],
        ),
        returnValue: _i5.Future<String>.value(_i8.dummyValue<String>(
          this,
          Invocation.method(
            #getSceneImageWithFallback,
            [
              storyId,
              imageName,
            ],
          ),
        )),
      ) as _i5.Future<String>);

  @override
  _i5.Future<String> getCharacterImageWithFallback(
    String? storyId,
    String? imageName,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCharacterImageWithFallback,
          [
            storyId,
            imageName,
          ],
        ),
        returnValue: _i5.Future<String>.value(_i8.dummyValue<String>(
          this,
          Invocation.method(
            #getCharacterImageWithFallback,
            [
              storyId,
              imageName,
            ],
          ),
        )),
      ) as _i5.Future<String>);

  @override
  _i5.Future<String> getBackgroundMusicWithFallback(
    String? storyId,
    String? musicName,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #getBackgroundMusicWithFallback,
          [
            storyId,
            musicName,
          ],
        ),
        returnValue: _i5.Future<String>.value(_i8.dummyValue<String>(
          this,
          Invocation.method(
            #getBackgroundMusicWithFallback,
            [
              storyId,
              musicName,
            ],
          ),
        )),
      ) as _i5.Future<String>);

  @override
  _i5.Future<String> getSoundEffectWithFallback(
    String? storyId,
    String? soundName,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #getSoundEffectWithFallback,
          [
            storyId,
            soundName,
          ],
        ),
        returnValue: _i5.Future<String>.value(_i8.dummyValue<String>(
          this,
          Invocation.method(
            #getSoundEffectWithFallback,
            [
              storyId,
              soundName,
            ],
          ),
        )),
      ) as _i5.Future<String>);

  @override
  _i5.Future<String> getVocabularyImageWithFallback(
    String? storyId,
    String? imageName,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #getVocabularyImageWithFallback,
          [
            storyId,
            imageName,
          ],
        ),
        returnValue: _i5.Future<String>.value(_i8.dummyValue<String>(
          this,
          Invocation.method(
            #getVocabularyImageWithFallback,
            [
              storyId,
              imageName,
            ],
          ),
        )),
      ) as _i5.Future<String>);

  @override
  _i5.Future<bool> validateFallbackAssets() => (super.noSuchMethod(
        Invocation.method(
          #validateFallbackAssets,
          [],
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  void clearCache() => super.noSuchMethod(
        Invocation.method(
          #clearCache,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [StoryRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockStoryRepository extends _i1.Mock implements _i9.StoryRepository {
  MockStoryRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<List<_i6.StoryMetadataModel>> fetchStoryMetadataList() =>
      (super.noSuchMethod(
        Invocation.method(
          #fetchStoryMetadataList,
          [],
        ),
        returnValue: _i5.Future<List<_i6.StoryMetadataModel>>.value(
            <_i6.StoryMetadataModel>[]),
      ) as _i5.Future<List<_i6.StoryMetadataModel>>);

  @override
  _i5.Future<_i2.StoryModel> fetchStoryById(
    String? storyId, {
    String? dataSource,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #fetchStoryById,
          [storyId],
          {#dataSource: dataSource},
        ),
        returnValue: _i5.Future<_i2.StoryModel>.value(_FakeStoryModel_0(
          this,
          Invocation.method(
            #fetchStoryById,
            [storyId],
            {#dataSource: dataSource},
          ),
        )),
      ) as _i5.Future<_i2.StoryModel>);

  @override
  _i5.Future<bool> storyExists(String? storyId) => (super.noSuchMethod(
        Invocation.method(
          #storyExists,
          [storyId],
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  void clearCache() => super.noSuchMethod(
        Invocation.method(
          #clearCache,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  Map<String, dynamic> getCacheStats() => (super.noSuchMethod(
        Invocation.method(
          #getCacheStats,
          [],
        ),
        returnValue: <String, dynamic>{},
      ) as Map<String, dynamic>);

  @override
  _i5.Future<bool> downloadStoryFromFirebase(
    String? storyId, {
    dynamic Function(double)? onProgress,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #downloadStoryFromFirebase,
          [storyId],
          {#onProgress: onProgress},
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<bool> isStoryAvailableInFirebase(String? storyId) =>
      (super.noSuchMethod(
        Invocation.method(
          #isStoryAvailableInFirebase,
          [storyId],
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<bool> isStoryDownloadedLocally(String? storyId) =>
      (super.noSuchMethod(
        Invocation.method(
          #isStoryDownloadedLocally,
          [storyId],
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<String?> getLocalStoryPath(String? storyId) => (super.noSuchMethod(
        Invocation.method(
          #getLocalStoryPath,
          [storyId],
        ),
        returnValue: _i5.Future<String?>.value(),
      ) as _i5.Future<String?>);

  @override
  _i5.Future<List<String>> getFirebaseStoryList() => (super.noSuchMethod(
        Invocation.method(
          #getFirebaseStoryList,
          [],
        ),
        returnValue: _i5.Future<List<String>>.value(<String>[]),
      ) as _i5.Future<List<String>>);

  @override
  _i5.Future<Map<String, dynamic>> getStoryAvailabilityInfo(String? storyId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getStoryAvailabilityInfo,
          [storyId],
        ),
        returnValue:
            _i5.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i5.Future<Map<String, dynamic>>);

  @override
  _i5.Future<String> getStoryStatus(String? storyId) => (super.noSuchMethod(
        Invocation.method(
          #getStoryStatus,
          [storyId],
        ),
        returnValue: _i5.Future<String>.value(_i8.dummyValue<String>(
          this,
          Invocation.method(
            #getStoryStatus,
            [storyId],
          ),
        )),
      ) as _i5.Future<String>);

  @override
  _i5.Future<bool> downloadStory(
    String? storyId, {
    dynamic Function(double)? onProgress,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #downloadStory,
          [storyId],
          {#onProgress: onProgress},
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);
}

/// A class which mocks [StoryDownloadManager].
///
/// See the documentation for Mockito's code generation for more information.
class MockStoryDownloadManager extends _i1.Mock
    implements _i10.StoryDownloadManager {
  MockStoryDownloadManager() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Stream<_i11.DownloadProgress> get downloadProgress => (super.noSuchMethod(
        Invocation.getter(#downloadProgress),
        returnValue: _i5.Stream<_i11.DownloadProgress>.empty(),
      ) as _i5.Stream<_i11.DownloadProgress>);

  @override
  List<_i11.DownloadProgress> get activeDownloads => (super.noSuchMethod(
        Invocation.getter(#activeDownloads),
        returnValue: <_i11.DownloadProgress>[],
      ) as List<_i11.DownloadProgress>);

  @override
  _i11.DownloadProgress? getDownloadProgress(String? storyId) =>
      (super.noSuchMethod(Invocation.method(
        #getDownloadProgress,
        [storyId],
      )) as _i11.DownloadProgress?);

  @override
  _i5.Future<bool> downloadStory(
    String? storyId, {
    dynamic Function(double)? onProgress,
    bool? checkPermissions = true,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #downloadStory,
          [storyId],
          {
            #onProgress: onProgress,
            #checkPermissions: checkPermissions,
          },
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<void> cancelDownload(String? storyId) => (super.noSuchMethod(
        Invocation.method(
          #cancelDownload,
          [storyId],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<bool> deleteDownloadedStory(String? storyId) =>
      (super.noSuchMethod(
        Invocation.method(
          #deleteDownloadedStory,
          [storyId],
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  bool isDownloading(String? storyId) => (super.noSuchMethod(
        Invocation.method(
          #isDownloading,
          [storyId],
        ),
        returnValue: false,
      ) as bool);

  @override
  bool isDownloadFailed(String? storyId) => (super.noSuchMethod(
        Invocation.method(
          #isDownloadFailed,
          [storyId],
        ),
        returnValue: false,
      ) as bool);

  @override
  double getDownloadProgressPercentage(String? storyId) => (super.noSuchMethod(
        Invocation.method(
          #getDownloadProgressPercentage,
          [storyId],
        ),
        returnValue: 0.0,
      ) as double);

  @override
  _i5.Future<List<_i3.DownloadedStoryInfo>> getDownloadedStoriesInfo() =>
      (super.noSuchMethod(
        Invocation.method(
          #getDownloadedStoriesInfo,
          [],
        ),
        returnValue: _i5.Future<List<_i3.DownloadedStoryInfo>>.value(
            <_i3.DownloadedStoryInfo>[]),
      ) as _i5.Future<List<_i3.DownloadedStoryInfo>>);

  @override
  _i5.Future<_i3.CleanupResult> performManualCleanup() => (super.noSuchMethod(
        Invocation.method(
          #performManualCleanup,
          [],
        ),
        returnValue: _i5.Future<_i3.CleanupResult>.value(_FakeCleanupResult_1(
          this,
          Invocation.method(
            #performManualCleanup,
            [],
          ),
        )),
      ) as _i5.Future<_i3.CleanupResult>);

  @override
  _i5.Future<void> updateStoryAccess(String? storyId) => (super.noSuchMethod(
        Invocation.method(
          #updateStoryAccess,
          [storyId],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );
}
