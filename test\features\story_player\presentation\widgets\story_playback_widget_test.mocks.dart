// Mocks generated by <PERSON><PERSON><PERSON> 5.4.4 from annotations
// in choice_once_upon_a_time/test/features/story_player/presentation/widgets/story_playback_widget_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:choice_once_upon_a_time/core/services/story_narration_service_interface.dart'
    as _i3;
import 'package:choice_once_upon_a_time/core/services/story_settings_service.dart'
    as _i6;
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart'
    as _i5;
import 'package:choice_once_upon_a_time/models/narration_models.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeNarrationState_0 extends _i1.SmartFake
    implements _i2.NarrationState {
  _FakeNarrationState_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeNarrationConfig_1 extends _i1.SmartFake
    implements _i2.NarrationConfig {
  _FakeNarrationConfig_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [IStoryNarrationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockIStoryNarrationService extends _i1.Mock
    implements _i3.IStoryNarrationService {
  MockIStoryNarrationService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Stream<_i2.NarrationState> get stateStream => (super.noSuchMethod(
        Invocation.getter(#stateStream),
        returnValue: _i4.Stream<_i2.NarrationState>.empty(),
      ) as _i4.Stream<_i2.NarrationState>);

  @override
  _i4.Stream<_i2.NarrationProgress> get progressStream => (super.noSuchMethod(
        Invocation.getter(#progressStream),
        returnValue: _i4.Stream<_i2.NarrationProgress>.empty(),
      ) as _i4.Stream<_i2.NarrationProgress>);

  @override
  _i4.Stream<_i2.WordHighlight> get wordHighlightStream => (super.noSuchMethod(
        Invocation.getter(#wordHighlightStream),
        returnValue: _i4.Stream<_i2.WordHighlight>.empty(),
      ) as _i4.Stream<_i2.WordHighlight>);

  @override
  _i2.NarrationState get currentState => (super.noSuchMethod(
        Invocation.getter(#currentState),
        returnValue: _FakeNarrationState_0(
          this,
          Invocation.getter(#currentState),
        ),
      ) as _i2.NarrationState);

  @override
  _i2.NarrationConfig get currentConfig => (super.noSuchMethod(
        Invocation.getter(#currentConfig),
        returnValue: _FakeNarrationConfig_1(
          this,
          Invocation.getter(#currentConfig),
        ),
      ) as _i2.NarrationConfig);

  @override
  bool get isInitialized => (super.noSuchMethod(
        Invocation.getter(#isInitialized),
        returnValue: false,
      ) as bool);

  @override
  bool get isNarrating => (super.noSuchMethod(
        Invocation.getter(#isNarrating),
        returnValue: false,
      ) as bool);

  @override
  bool get isPaused => (super.noSuchMethod(
        Invocation.getter(#isPaused),
        returnValue: false,
      ) as bool);

  @override
  _i4.Future<void> initialize({_i2.NarrationConfig? config}) =>
      (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
          {#config: config},
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> configure(_i2.NarrationConfig? config) =>
      (super.noSuchMethod(
        Invocation.method(
          #configure,
          [config],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> narrateScene(
    _i5.EnhancedSceneModel? scene, {
    String? storyId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #narrateScene,
          [scene],
          {#storyId: storyId},
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> narrateText(
    String? text, {
    String? emotionCue,
    String? storyId,
    String? sceneId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #narrateText,
          [text],
          {
            #emotionCue: emotionCue,
            #storyId: storyId,
            #sceneId: sceneId,
          },
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> play() => (super.noSuchMethod(
        Invocation.method(
          #play,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> pause() => (super.noSuchMethod(
        Invocation.method(
          #pause,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> stop() => (super.noSuchMethod(
        Invocation.method(
          #stop,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> skipToNextSentence() => (super.noSuchMethod(
        Invocation.method(
          #skipToNextSentence,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> skipToPreviousSentence() => (super.noSuchMethod(
        Invocation.method(
          #skipToPreviousSentence,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> seekToWord(int? wordIndex) => (super.noSuchMethod(
        Invocation.method(
          #seekToWord,
          [wordIndex],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> seekToSentence(int? sentenceIndex) => (super.noSuchMethod(
        Invocation.method(
          #seekToSentence,
          [sentenceIndex],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> replayCurrentSentence() => (super.noSuchMethod(
        Invocation.method(
          #replayCurrentSentence,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> replayScene() => (super.noSuchMethod(
        Invocation.method(
          #replayScene,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> setSpeechRate(double? rate) => (super.noSuchMethod(
        Invocation.method(
          #setSpeechRate,
          [rate],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> setSpeechPitch(double? pitch) => (super.noSuchMethod(
        Invocation.method(
          #setSpeechPitch,
          [pitch],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> setSpeechVolume(double? volume) => (super.noSuchMethod(
        Invocation.method(
          #setSpeechVolume,
          [volume],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> setAutoProgression(bool? enabled) => (super.noSuchMethod(
        Invocation.method(
          #setAutoProgression,
          [enabled],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> setWordHighlighting(bool? enabled) => (super.noSuchMethod(
        Invocation.method(
          #setWordHighlighting,
          [enabled],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> saveProgress() => (super.noSuchMethod(
        Invocation.method(
          #saveProgress,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> loadProgress(
    String? storyId,
    String? sceneId,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #loadProgress,
          [
            storyId,
            sceneId,
          ],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> clearProgress(
    String? storyId,
    String? sceneId,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #clearProgress,
          [
            storyId,
            sceneId,
          ],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> dispose() => (super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);
}

/// A class which mocks [StorySettingsService].
///
/// See the documentation for Mockito's code generation for more information.
class MockStorySettingsService extends _i1.Mock
    implements _i6.StorySettingsService {
  MockStorySettingsService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Stream<double> get subtitleSizeStream => (super.noSuchMethod(
        Invocation.getter(#subtitleSizeStream),
        returnValue: _i4.Stream<double>.empty(),
      ) as _i4.Stream<double>);

  @override
  _i4.Stream<double> get narrationSpeedStream => (super.noSuchMethod(
        Invocation.getter(#narrationSpeedStream),
        returnValue: _i4.Stream<double>.empty(),
      ) as _i4.Stream<double>);

  @override
  _i4.Stream<bool> get subtitlesEnabledStream => (super.noSuchMethod(
        Invocation.getter(#subtitlesEnabledStream),
        returnValue: _i4.Stream<bool>.empty(),
      ) as _i4.Stream<bool>);

  @override
  _i4.Stream<bool> get autoPlayStream => (super.noSuchMethod(
        Invocation.getter(#autoPlayStream),
        returnValue: _i4.Stream<bool>.empty(),
      ) as _i4.Stream<bool>);

  @override
  _i4.Stream<double> get controlRowTransparencyStream => (super.noSuchMethod(
        Invocation.getter(#controlRowTransparencyStream),
        returnValue: _i4.Stream<double>.empty(),
      ) as _i4.Stream<double>);

  @override
  _i4.Stream<double> get textFontSizeStream => (super.noSuchMethod(
        Invocation.getter(#textFontSizeStream),
        returnValue: _i4.Stream<double>.empty(),
      ) as _i4.Stream<double>);

  @override
  _i4.Stream<double> get overlayScaleStream => (super.noSuchMethod(
        Invocation.getter(#overlayScaleStream),
        returnValue: _i4.Stream<double>.empty(),
      ) as _i4.Stream<double>);

  @override
  _i4.Stream<int> get sceneTransitionDurationStream => (super.noSuchMethod(
        Invocation.getter(#sceneTransitionDurationStream),
        returnValue: _i4.Stream<int>.empty(),
      ) as _i4.Stream<int>);

  @override
  _i4.Stream<int> get choiceTransitionDurationStream => (super.noSuchMethod(
        Invocation.getter(#choiceTransitionDurationStream),
        returnValue: _i4.Stream<int>.empty(),
      ) as _i4.Stream<int>);

  @override
  double get subtitleSize => (super.noSuchMethod(
        Invocation.getter(#subtitleSize),
        returnValue: 0.0,
      ) as double);

  @override
  double get narrationSpeed => (super.noSuchMethod(
        Invocation.getter(#narrationSpeed),
        returnValue: 0.0,
      ) as double);

  @override
  bool get subtitlesEnabled => (super.noSuchMethod(
        Invocation.getter(#subtitlesEnabled),
        returnValue: false,
      ) as bool);

  @override
  bool get autoPlay => (super.noSuchMethod(
        Invocation.getter(#autoPlay),
        returnValue: false,
      ) as bool);

  @override
  double get controlRowTransparency => (super.noSuchMethod(
        Invocation.getter(#controlRowTransparency),
        returnValue: 0.0,
      ) as double);

  @override
  double get textFontSize => (super.noSuchMethod(
        Invocation.getter(#textFontSize),
        returnValue: 0.0,
      ) as double);

  @override
  double get overlayScale => (super.noSuchMethod(
        Invocation.getter(#overlayScale),
        returnValue: 0.0,
      ) as double);

  @override
  int get sceneTransitionDuration => (super.noSuchMethod(
        Invocation.getter(#sceneTransitionDuration),
        returnValue: 0,
      ) as int);

  @override
  int get choiceTransitionDuration => (super.noSuchMethod(
        Invocation.getter(#choiceTransitionDuration),
        returnValue: 0,
      ) as int);

  @override
  bool get autoSceneProgression => (super.noSuchMethod(
        Invocation.getter(#autoSceneProgression),
        returnValue: false,
      ) as bool);

  @override
  _i4.Future<void> initialize() => (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> setSubtitleSize(double? size) => (super.noSuchMethod(
        Invocation.method(
          #setSubtitleSize,
          [size],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> setNarrationSpeed(double? speed) => (super.noSuchMethod(
        Invocation.method(
          #setNarrationSpeed,
          [speed],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> setSubtitlesEnabled(bool? enabled) => (super.noSuchMethod(
        Invocation.method(
          #setSubtitlesEnabled,
          [enabled],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> setAutoPlay(bool? enabled) => (super.noSuchMethod(
        Invocation.method(
          #setAutoPlay,
          [enabled],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> setAutoSceneProgression(bool? enabled) =>
      (super.noSuchMethod(
        Invocation.method(
          #setAutoSceneProgression,
          [enabled],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> setControlRowTransparency(double? transparency) =>
      (super.noSuchMethod(
        Invocation.method(
          #setControlRowTransparency,
          [transparency],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> setTextFontSize(double? size) => (super.noSuchMethod(
        Invocation.method(
          #setTextFontSize,
          [size],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> setOverlayScale(double? scale) => (super.noSuchMethod(
        Invocation.method(
          #setOverlayScale,
          [scale],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> setSceneTransitionDuration(int? duration) =>
      (super.noSuchMethod(
        Invocation.method(
          #setSceneTransitionDuration,
          [duration],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> setChoiceTransitionDuration(int? duration) =>
      (super.noSuchMethod(
        Invocation.method(
          #setChoiceTransitionDuration,
          [duration],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> resetToDefaults() => (super.noSuchMethod(
        Invocation.method(
          #resetToDefaults,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  Map<String, dynamic> getSettingsMap() => (super.noSuchMethod(
        Invocation.method(
          #getSettingsMap,
          [],
        ),
        returnValue: <String, dynamic>{},
      ) as Map<String, dynamic>);

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );
}
