# Choice: Once Upon A Time - File Catalog

This document provides a comprehensive catalog of all files in the project, their purposes, and update schedules.

## 🗂️ Project Structure Overview

```
lib/
├── app/                    # App-wide configuration
│   ├── providers/         # Global Riverpod providers
│   ├── routing/          # GoRouter configuration
│   └── theme/            # Global theme definitions
├── core/                  # Core utilities and services
│   ├── audio/            # TTS and sound services
│   ├── network/          # Network connectivity
│   ├── storage/          # Offline storage service
│   └── utils/            # Utility functions
├── features/             # Feature modules
│   ├── app_init/        # App initialization & FTUE
│   ├── auth/            # Authentication
│   ├── parent_zone/     # Parent dashboard
│   ├── story_library/   # Story browsing & selection
│   ├── story_player/    # Story playback & interaction
│   └── subscription/    # IAP and subscription
├── models/              # Shared data models
├── shared_widgets/      # Reusable UI components
├── l10n/               # Localization files
└── main.dart           # App entry point
```

## 📁 Core Files

### Entry Point
- `lib/main.dart`
  - Purpose: Application entry point, initializes services and providers
  - Updates: When adding new global providers or changing initialization logic

### App Configuration
- `lib/app/providers/app_providers.dart`
  - Purpose: Global provider definitions and initialization
  - Updates: When adding new global state or services
- `lib/app/routing/app_router.dart`
  - Purpose: GoRouter configuration and route definitions
  - Updates: When adding new screens or changing navigation
- `lib/app/theme/app_theme.dart`
  - Purpose: Global theme data and styling
  - Updates: When modifying app-wide styling or adding new themes

## 🎯 Feature Modules

### App Initialization (app_init/)
- `lib/features/app_init/presentation/screens/launch_screen.dart`
  - Purpose: Initial app launch screen
  - Updates: When modifying launch experience
- `lib/features/app_init/presentation/screens/ftue_screen.dart`
  - Purpose: First Time User Experience
  - Updates: When changing onboarding flow

### Story Library (story_library/)
- `lib/features/story_library/presentation/screens/story_library_screen.dart`
  - Purpose: Main story browsing screen
  - Updates: When modifying story grid or library features
- `lib/features/story_library/data/story_repository.dart`
  - Purpose: Story data access and caching
  - Updates: When changing story data structure or fetching logic

### Story Player (story_player/)
- `lib/features/story_player/presentation/screens/story_player_screen.dart`
  - Purpose: Interactive story playback screen
  - Updates: When modifying story interaction or playback
- `lib/features/story_player/providers/story_player_provider.dart`
  - Purpose: Story playback state management
  - Updates: When changing playback logic or story interactions

### Parent Zone (parent_zone/)
- `lib/features/parent_zone/presentation/screens/parent_zone_dashboard.dart`
  - Purpose: Parent settings and controls
  - Updates: When adding new parent features
- `lib/features/parent_zone/presentation/screens/sound_settings_screen.dart`
  - Purpose: Audio control settings
  - Updates: When modifying audio settings

### Authentication (auth/)
- `lib/features/auth/presentation/screens/parental_gate_screen.dart`
  - Purpose: Parent authentication gate
  - Updates: When modifying security measures
- `lib/features/auth/providers/auth_provider.dart`
  - Purpose: Authentication state management
  - Updates: When changing auth logic

### Subscription (subscription/)
- `lib/features/subscription/presentation/screens/subscription_screen.dart`
  - Purpose: IAP and subscription management
  - Updates: When modifying subscription features
- `lib/features/subscription/providers/subscription_provider.dart`
  - Purpose: Subscription state management
  - Updates: When changing subscription logic

## 🛠️ Core Services

### Audio Services
- `lib/core/audio/tts_service.dart`
  - Purpose: Text-to-speech functionality
  - Updates: When modifying narrator capabilities
- `lib/core/audio/sound_effect_service.dart`
  - Purpose: Sound effects playback
  - Updates: When adding new sound features

### Storage Service
- `lib/core/storage/offline_storage_service.dart`
  - Purpose: Local data persistence
  - Updates: When modifying offline capabilities
- `lib/core/storage/file_storage_service.dart`
  - Purpose: Asset file management
  - Updates: When changing asset handling

### Network Service
- `lib/core/network/connectivity_service.dart`
  - Purpose: Network state monitoring
  - Updates: When modifying network handling

## 📊 Models
- `lib/models/story_model.dart`
  - Purpose: Story data structure
  - Updates: When modifying story content structure
- `lib/models/scene_model.dart`
  - Purpose: Scene data structure
  - Updates: When changing scene formats
- `lib/models/user_model.dart`
  - Purpose: User data structure
  - Updates: When modifying user data fields

## 🎨 Shared Widgets
- `lib/shared_widgets/primary_button.dart`
  - Purpose: Standard button component
  - Updates: When modifying button styles
- `lib/shared_widgets/loading_indicator.dart`
  - Purpose: Loading state display
  - Updates: When changing loading animations

## 🌐 Configuration Files
- `pubspec.yaml`
  - Purpose: Project dependencies and metadata
  - Updates: When adding/updating dependencies
- `firebase.json`
  - Purpose: Firebase configuration
  - Updates: When modifying Firebase setup
- `android/app/build.gradle`
  - Purpose: Android build configuration
  - Updates: When changing Android settings
- `ios/Runner.xcodeproj`
  - Purpose: iOS build configuration
  - Updates: When changing iOS settings

## 📝 Documentation
- `README.md`
  - Purpose: Project overview and setup instructions
  - Updates: When changing setup process or features
- `CONTEXT_REFERENCE.md`
  - Purpose: Technical implementation details
  - Updates: When modifying architecture or implementation
- `FILE_CATALOG.md` (this file)
  - Purpose: File documentation and update tracking
  - Updates: When adding/removing files or changing structure

## 🧪 Test Files
- `test/widget_test.dart`
  - Purpose: Widget testing
  - Updates: When adding new widgets
- `test/unit/models/*_test.dart`
  - Purpose: Model unit tests
  - Updates: When modifying data models
- `test/integration/*_test.dart`
  - Purpose: Integration tests
  - Updates: When changing feature interactions

## Update Schedule

### Daily Updates
- Story content files
- UI component tweaks
- Bug fixes in any component

### Weekly Updates
- Test files
- Documentation updates
- Performance optimizations

### Monthly Updates
- Security configurations
- Major feature implementations
- Dependency updates

### As-Needed Updates
- Build configurations
- Asset files
- Localization strings
- Firebase configurations

## Dependencies Between Files

### Critical Paths
1. main.dart → app_providers.dart → core services
2. app_router.dart → all screen files
3. story_player_provider.dart → tts_service.dart
4. auth_provider.dart → all protected screens

### Feature Dependencies
1. Story Library → Offline Storage → Network Service
2. Story Player → TTS Service → Sound Effect Service
3. Parent Zone → Auth Service → Subscription Service

## Maintenance Guidelines

1. Always update tests when modifying models or providers
2. Keep documentation in sync with code changes
3. Review security implications when modifying auth flows
4. Test offline functionality when changing storage logic
5. Verify accessibility when updating UI components
6. Ensure backward compatibility when modifying data structures
