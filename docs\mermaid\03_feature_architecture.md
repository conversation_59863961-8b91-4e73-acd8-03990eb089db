# Feature Architecture

## Feature-First Structure

```mermaid
graph TD
    subgraph App["lib/app/"]
        AppConfig[App Configuration]
        Routing[GoRouter Setup]
        Theme[Global Theme]
        Providers[Global Providers]
    end
    
    subgraph Core["lib/core/"]
        Audio[Audio Services]
        Storage[Storage Services]
        Utils[Utilities]
        Services[Core Services]
    end
    
    subgraph Features["lib/features/"]
        AppInit[app_init/]
        StoryLibrary[story_library/]
        StoryPlayer[story_player/]
        ParentZone[parent_zone/]
        Auth[auth/]
    end
    
    subgraph Models["lib/models/"]
        StoryModels[Story Models]
        UserModels[User Models]
        ProgressModels[Progress Models]
    end
    
    subgraph SharedWidgets["lib/shared_widgets/"]
        CommonUI[Common UI Components]
        LoadingIndicators[Loading Indicators]
        CustomButtons[Custom Buttons]
    end
    
    Features --> Core
    Features --> Models
    Features --> SharedWidgets
    App --> Core
    App --> Features
    
    style App fill:#e3f2fd
    style Core fill:#e8f5e8
    style Features fill:#fff3e0
    style Models fill:#fce4ec
    style SharedWidgets fill:#f3e5f5
```

## Story Library Feature

```mermaid
graph TD
    subgraph StoryLibraryFeature["features/story_library/"]
        subgraph Presentation["presentation/"]
            HomeScreen[home_screen.dart]
            LibraryScreen[story_library_screen.dart]
            StoryCard[story_card_widget.dart]
            CategoryFilter[category_filter_widget.dart]
        end
        
        subgraph Data["data/"]
            StoryRepository[story_repository.dart]
            AssetStoryService[asset_story_service.dart]
            EnhancedStoryRepository[enhanced_story_repository.dart]
        end
        
        subgraph Domain["domain/"]
            StoryEntity[story_entity.dart]
            LoadStoriesUseCase[load_stories_use_case.dart]
        end
    end
    
    Presentation --> Domain
    Domain --> Data
    
    style Presentation fill:#e8eaf6
    style Data fill:#e0f2f1
    style Domain fill:#fff8e1
```

## Story Player Feature

```mermaid
graph TD
    subgraph StoryPlayerFeature["features/story_player/"]
        subgraph Presentation["presentation/"]
            PlayerScreen[story_player_screen.dart]
            PlaybackWidget[story_playback_widget.dart]
            NarrationControls[narration_controls_widget.dart]
            ChoiceButtons[choice_buttons_widget.dart]
            ProgressIndicator[progress_indicator_widget.dart]
        end
        
        subgraph Services["services/"]
            NarrationService[story_narration_service.dart]
            ProgressTracker[progress_tracking_service.dart]
            SettingsService[story_settings_service.dart]
        end
        
        subgraph Widgets["widgets/"]
            WordHighlight[word_highlight_widget.dart]
            SceneTransition[scene_transition_widget.dart]
            VoiceGuide[voice_guide_widget.dart]
        end
    end
    
    Presentation --> Services
    Presentation --> Widgets
    
    style Presentation fill:#e8eaf6
    style Services fill:#e0f2f1
    style Widgets fill:#fff8e1
```

## Parent Zone Feature

```mermaid
graph TD
    subgraph ParentZoneFeature["features/parent_zone/"]
        subgraph Presentation["presentation/"]
            ParentGate[parent_gate_screen.dart]
            Dashboard[parent_dashboard_screen.dart]
            ProgressScreen[progress_tracking_screen.dart]
            SettingsScreen[settings_screen.dart]
            ProfileScreen[profile_management_screen.dart]
        end
        
        subgraph Data["data/"]
            UserProfileService[user_profile_service.dart]
            ProgressRepository[progress_repository.dart]
            SettingsRepository[settings_repository.dart]
        end
        
        subgraph Widgets["widgets/"]
            ProgressChart[progress_chart_widget.dart]
            ProfileCard[profile_card_widget.dart]
            SettingsPanel[settings_panel_widget.dart]
        end
    end
    
    Presentation --> Data
    Presentation --> Widgets
    
    style Presentation fill:#e8eaf6
    style Data fill:#e0f2f1
    style Widgets fill:#fff8e1
```
