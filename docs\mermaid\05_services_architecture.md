# Services Architecture

## Core Services Overview

```mermaid
graph TD
    subgraph AudioServices["Audio Services"]
        TTSService[TTS Service]
        NarrationService[Story Narration Service]
        SoundEffectService[Sound Effect Service]
        VoiceGuidanceService[Voice Guidance Service]
    end
    
    subgraph StorageServices["Storage Services"]
        AssetService[Asset Loading Service]
        SettingsService[Settings Service]
        ProgressService[Progress Tracking Service]
        CacheService[Cache Service]
    end
    
    subgraph StoryServices["Story Services"]
        StoryRepository[Story Repository]
        SceneService[Scene Management Service]
        ChoiceService[Choice Handling Service]
        RewardsService[Rewards Service]
    end
    
    subgraph ParentServices["Parent Services"]
        ProfileService[User Profile Service]
        AuthService[Authentication Service]
        AnalyticsService[Analytics Service]
    end
    
    AudioServices --> StorageServices
    StoryServices --> AudioServices
    StoryServices --> StorageServices
    ParentServices --> StorageServices
    
    style AudioServices fill:#e3f2fd
    style StorageServices fill:#e8f5e8
    style StoryServices fill:#fff3e0
    style ParentServices fill:#fce4ec
```

## TTS and Narration Service Architecture

```mermaid
graph TD
    subgraph NarrationLayer["Narration Layer"]
        EnhancedNarrationService[Enhanced Story Narration Service]
        NarrationInterface[IStoryNarrationService]
        NarrationControls[Narration Controls Widget]
    end
    
    subgraph TTSLayer["TTS Layer"]
        EnhancedTTSService[Enhanced Narration TTS Service]
        TTSInterface[IEnhancedNarrationTTSService]
        UnifiedTTSService[Unified TTS Service]
    end
    
    subgraph PlatformLayer["Platform Layer"]
        FlutterTTS[flutter_tts Plugin]
        NativeTTS[Native TTS Engines]
        AudioSystem[Platform Audio System]
    end
    
    subgraph SupportingServices["Supporting Services"]
        EmotionMapper[Emotion Cue Mapper]
        VoiceGuidance[Voice Guidance Manager]
        SettingsService[Story Settings Service]
    end
    
    NarrationControls --> NarrationInterface
    EnhancedNarrationService --> NarrationInterface
    EnhancedNarrationService --> TTSInterface
    EnhancedTTSService --> TTSInterface
    EnhancedTTSService --> UnifiedTTSService
    UnifiedTTSService --> FlutterTTS
    FlutterTTS --> NativeTTS
    NativeTTS --> AudioSystem
    
    EnhancedNarrationService --> EmotionMapper
    EnhancedNarrationService --> VoiceGuidance
    EnhancedNarrationService --> SettingsService
    
    style NarrationLayer fill:#e8eaf6
    style TTSLayer fill:#e0f2f1
    style PlatformLayer fill:#fff8e1
    style SupportingServices fill:#fce4ec
```

## Story Loading Service Architecture

```mermaid
graph TD
    subgraph RepositoryLayer["Repository Layer"]
        EnhancedStoryRepository[Enhanced Story Repository]
        AssetOnlyStoryService[Asset Only Story Service]
        NewStoryService[New Story Service]
    end
    
    subgraph DataSources["Data Sources"]
        AssetBundle[Asset Bundle]
        LocalCache[Local Cache]
        SharedPreferences[Shared Preferences]
        FutureFirebase[Future: Firebase Storage]
    end
    
    subgraph Models["Story Models"]
        EnhancedStoryModel[Enhanced Story Model]
        StoryMetadataModel[Story Metadata Model]
        SceneModel[Scene Model]
    end
    
    subgraph Caching["Caching Layer"]
        StoryCache[Story Cache]
        ImageCache[Image Cache]
        MetadataCache[Metadata Cache]
    end
    
    EnhancedStoryRepository --> AssetOnlyStoryService
    EnhancedStoryRepository --> NewStoryService
    AssetOnlyStoryService --> AssetBundle
    NewStoryService --> AssetBundle
    AssetOnlyStoryService --> LocalCache
    NewStoryService --> SharedPreferences
    
    EnhancedStoryRepository --> EnhancedStoryModel
    AssetOnlyStoryService --> StoryMetadataModel
    NewStoryService --> SceneModel
    
    EnhancedStoryRepository --> StoryCache
    AssetOnlyStoryService --> ImageCache
    NewStoryService --> MetadataCache
    
    style RepositoryLayer fill:#e8eaf6
    style DataSources fill:#e0f2f1
    style Models fill:#fff8e1
    style Caching fill:#fce4ec
```

## Service Dependencies and Interactions

```mermaid
graph TD
    subgraph UILayer["UI Layer"]
        StoryPlayerScreen[Story Player Screen]
        NarrationControlsWidget[Narration Controls Widget]
        StoryPlaybackWidget[Story Playback Widget]
    end
    
    subgraph ServiceLayer["Service Layer"]
        StoryNarrationService[Story Narration Service]
        StorySettingsService[Story Settings Service]
        ChildProgressService[Child Progress Service]
        StoryRewardsService[Story Rewards Service]
    end
    
    subgraph CoreServices["Core Services"]
        TTSService[TTS Service]
        VoiceGuidanceManager[Voice Guidance Manager]
        AppLogger[App Logger]
    end
    
    subgraph DataLayer["Data Layer"]
        StoryRepository[Story Repository]
        UserProfileService[User Profile Service]
        SharedPreferences[Shared Preferences]
    end
    
    StoryPlayerScreen --> StoryNarrationService
    StoryPlayerScreen --> StorySettingsService
    StoryPlayerScreen --> ChildProgressService
    StoryPlayerScreen --> StoryRewardsService
    
    NarrationControlsWidget --> StoryNarrationService
    StoryPlaybackWidget --> StoryNarrationService
    
    StoryNarrationService --> TTSService
    StoryNarrationService --> VoiceGuidanceManager
    StoryNarrationService --> AppLogger
    
    StorySettingsService --> SharedPreferences
    ChildProgressService --> UserProfileService
    StoryRewardsService --> StoryRepository
    
    style UILayer fill:#e8eaf6
    style ServiceLayer fill:#e0f2f1
    style CoreServices fill:#fff8e1
    style DataLayer fill:#fce4ec
```
