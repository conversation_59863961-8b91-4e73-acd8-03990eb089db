graph TB
    %% Main Application Layer
    subgraph "Application Entry"
        A[main.dart]
        B[AppWidget]
        C[ProviderScope]
    end
    
    %% Core Infrastructure
    subgraph "Core Infrastructure"
        D[AppRouter - GoRouter]
        E[AppTheme]
        F[AppLogger]
        G[AccessibilityService]
        H[StoryRewardsService]
    end
    
    %% State Management Layer
    subgraph "State Management - Riverpod"
        I[StoryLibraryProvider]
        J[UserProfileProvider]
        K[StoryProgressProvider]
        L[GlobalNarratorProvider]
        M[StorySettingsProvider]
    end
    
    %% Feature Modules
    subgraph "App Initialization"
        N[LaunchScreen]
        O[FTUEScreen]
        P[WelcomeScreen]
        Q[ProfileSelectionScreen]
    end
    
    subgraph "Story Library"
        R[HomeScreen]
        S[StoryLibraryScreen]
        T[ContinueStoryScreen]
        U[StoryRepository]
        V[StoryScannerService]
    end
    
    subgraph "Story Player"
        W[StoryIntroductionScreen]
        X[CharacterIntroductionScreen]
        Y[NewStoryPlayerScreen]
        Z[StoryPlaybackWidget]
        AA[StoryChoicePopupWidget]
        BB[StoryCompletionWidget]
    end
    
    subgraph "Authentication"
        CC[ParentalGateScreen]
        DD[ParentAuthScreen]
    end
    
    subgraph "Parent Zone"
        EE[ParentZoneDashboardScreen]
        FF[UserProfilesScreen]
        GG[ProgressTrackingScreen]
        HH[SoundSettingsScreen]
        II[NarratorCreationScreen]
    end
    
    %% Core Services Layer
    subgraph "Core Services"
        JJ[EnhancedStoryService]
        KK[EnhancedStoryNarrationService]
        LL[StoryProgressService]
        MM[StorySettingsService]
        NN[UserProfileService]
        OO[TTSService]
    end
    
    %% Data Layer
    subgraph "Data Sources"
        PP[AssetOnlyStoryService]
        QQ[FirebaseStoryService]
        RR[SharedPreferences]
        SS[Firebase Firestore]
        TT[Local Assets]
    end
    
    %% Global Components
    subgraph "Global UI Components"
        UU[GlobalNarratorWidget]
        VV[GlobalNarratorController]
        WW[LoadingIndicator]
        XX[ErrorWidget]
    end
    
    %% Connections - Entry Point
    A --> B
    B --> C
    B --> D
    B --> E
    B --> UU
    A --> F
    A --> G
    A --> H
    
    %% Connections - State Management
    C --> I
    C --> J
    C --> K
    C --> L
    C --> M
    
    %% Connections - Navigation Flow
    D --> N
    N --> O
    O --> P
    P --> Q
    Q --> R
    R --> S
    S --> W
    W --> X
    X --> Y
    
    %% Connections - Story Player
    Y --> Z
    Y --> AA
    Y --> BB
    Z --> KK
    KK --> OO
    
    %% Connections - Parent Zone
    R --> CC
    CC --> DD
    DD --> EE
    EE --> FF
    EE --> GG
    EE --> HH
    EE --> II
    
    %% Connections - Services to Providers
    I --> U
    I --> JJ
    J --> NN
    K --> LL
    L --> VV
    M --> MM
    
    %% Connections - Services to Data
    U --> PP
    U --> QQ
    U --> V
    JJ --> TT
    QQ --> SS
    NN --> RR
    MM --> RR
    LL --> RR
    
    %% Connections - Global Components
    VV --> OO
    UU --> VV
    
    %% Story Flow Connections
    S --> Y
    Y --> LL
    Y --> MM
    AA --> Y
    BB --> R
    
    %% Error Handling
    Y --> XX
    Z --> WW
    
    %% Style Definitions
    classDef entryPoint fill:#ff6b6b,stroke:#333,stroke-width:3px,color:#fff
    classDef infrastructure fill:#4ecdc4,stroke:#333,stroke-width:2px,color:#fff
    classDef stateManagement fill:#45b7d1,stroke:#333,stroke-width:2px,color:#fff
    classDef feature fill:#96ceb4,stroke:#333,stroke-width:2px,color:#333
    classDef service fill:#feca57,stroke:#333,stroke-width:2px,color:#333
    classDef data fill:#ff9ff3,stroke:#333,stroke-width:2px,color:#333
    classDef global fill:#54a0ff,stroke:#333,stroke-width:2px,color:#fff
    
    %% Apply Styles
    class A,B,C entryPoint
    class D,E,F,G,H infrastructure
    class I,J,K,L,M stateManagement
    class N,O,P,Q,R,S,T,W,X,Y,CC,DD,EE,FF,GG,HH,II feature
    class JJ,KK,LL,MM,NN,OO,U,V service
    class PP,QQ,RR,SS,TT data
    class UU,VV,WW,XX global
