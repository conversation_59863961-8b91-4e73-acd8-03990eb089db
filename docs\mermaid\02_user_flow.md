# User Flow Documentation

## Complete User Journey

```mermaid
flowchart TD
    Start([App Launch]) --> Launch[Launch Screen]
    Launch --> FirstTime{First Time User?}
    
    FirstTime -->|Yes| FTUE[FTUE Screen]
    FirstTime -->|No| Home[Home Screen]
    
    FTUE --> ProfileSelection[Profile Selection]
    ProfileSelection --> Welcome[Welcome Screen]
    Welcome --> Home
    
    Home --> StoryLibrary[Story Library]
    Home --> ParentGate[Parent Gate]
    
    StoryLibrary --> StorySelection{Select Story}
    StorySelection --> StoryIntro[Story Introduction]
    StoryIntro --> CharacterProfiles[Character Profiles]
    CharacterProfiles --> StoryPlayer[Story Player]
    
    StoryPlayer --> ScenePlay[Scene Playback]
    ScenePlay --> Choices{Has Choices?}
    Choices -->|Yes| ChoiceSelection[Choice Selection]
    Choices -->|No| NextScene[Next Scene]
    ChoiceSelection --> NextScene
    NextScene --> StoryComplete{Story Complete?}
    StoryComplete -->|No| ScenePlay
    StoryComplete -->|Yes| Completion[Story Completion]
    
    Completion --> Home
    
    ParentGate --> ParentAuth[Parent Authentication]
    ParentAuth --> ParentDashboard[Parent Dashboard]
    ParentDashboard --> ProgressTracking[Progress Tracking]
    ParentDashboard --> Settings[Settings]
    ParentDashboard --> ProfileManagement[Profile Management]
    
    style Start fill:#e8f5e8
    style Home fill:#e3f2fd
    style StoryPlayer fill:#fff3e0
    style ParentDashboard fill:#fce4ec
```

## Story Playback Flow

```mermaid
flowchart TD
    StoryStart([Story Selected]) --> Welcome[Welcome Screen]
    Welcome --> Characters[Character Introduction]
    Characters --> FirstScene[First Scene]
    
    FirstScene --> LoadImage[Load Scene Image]
    LoadImage --> StartNarration[Start TTS Narration]
    StartNarration --> WordHighlight[Word-by-Word Highlighting]
    WordHighlight --> NarrationComplete{Narration Complete?}
    
    NarrationComplete -->|No| WordHighlight
    NarrationComplete -->|Yes| CheckChoices{Has Choices?}
    
    CheckChoices -->|Yes| ShowChoices[Show Choice Buttons]
    CheckChoices -->|No| ShowNext[Show Next Button]
    
    ShowChoices --> UserChoice[User Selects Choice]
    ShowNext --> UserNext[User Taps Next]
    
    UserChoice --> NextScene[Navigate to Next Scene]
    UserNext --> NextScene
    
    NextScene --> StoryEnd{Story Complete?}
    StoryEnd -->|No| LoadImage
    StoryEnd -->|Yes| CompletionScreen[Story Completion]
    
    CompletionScreen --> Rewards[Show Rewards]
    Rewards --> BackToLibrary[Back to Library]
    
    style StoryStart fill:#e8f5e8
    style WordHighlight fill:#fff3e0
    style ShowChoices fill:#e3f2fd
    style CompletionScreen fill:#fce4ec
```

## Navigation Hierarchy

```mermaid
graph TD
    subgraph AppLevel["App Level"]
        Launch[Launch Screen]
        FTUE[FTUE Flow]
        Home[Home Screen]
    end
    
    subgraph StoryFlow["Story Flow"]
        Library[Story Library]
        Intro[Story Introduction]
        Player[Story Player]
        Completion[Story Completion]
    end
    
    subgraph ParentFlow["Parent Flow"]
        Gate[Parent Gate]
        Auth[Parent Auth]
        Dashboard[Parent Dashboard]
        Progress[Progress Tracking]
        Settings[Settings]
        Profiles[Profile Management]
    end
    
    Launch --> FTUE
    Launch --> Home
    FTUE --> Home
    
    Home --> Library
    Home --> Gate
    
    Library --> Intro
    Intro --> Player
    Player --> Completion
    Completion --> Library
    
    Gate --> Auth
    Auth --> Dashboard
    Dashboard --> Progress
    Dashboard --> Settings
    Dashboard --> Profiles
    
    style AppLevel fill:#e8eaf6
    style StoryFlow fill:#e0f2f1
    style ParentFlow fill:#fff8e1
```
