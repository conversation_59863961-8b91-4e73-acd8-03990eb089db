import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Story settings model for persistent storage
class StorySettings {
  final double fontSize;
  final double subtitleTransparency;
  final double narrationSpeed;
  final bool isAutoplayEnabled;

  const StorySettings({
    this.fontSize = 18.0,
    this.subtitleTransparency = 50.0,
    this.narrationSpeed = 1.0,
    this.isAutoplayEnabled = true,
  });

  StorySettings copyWith({
    double? fontSize,
    double? subtitleTransparency,
    double? narrationSpeed,
    bool? isAutoplayEnabled,
  }) {
    return StorySettings(
      fontSize: fontSize ?? this.fontSize,
      subtitleTransparency: subtitleTransparency ?? this.subtitleTransparency,
      narrationSpeed: narrationSpeed ?? this.narrationSpeed,
      isAutoplayEnabled: isAutoplayEnabled ?? this.isAutoplayEnabled,
    );
  }

  /// Save settings to SharedPreferences
  Future<void> save() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble('story_font_size', fontSize);
      await prefs.setDouble('story_subtitle_transparency', subtitleTransparency);
      await prefs.setDouble('story_narration_speed', narrationSpeed);
      await prefs.setBool('story_autoplay_enabled', isAutoplayEnabled);
      AppLogger.debug('[STORY_SETTINGS] Settings saved: fontSize=$fontSize, transparency=$subtitleTransparency, speed=$narrationSpeed, autoplay=$isAutoplayEnabled');
    } catch (e) {
      AppLogger.debug('[STORY_SETTINGS] Error saving settings: $e');
    }
  }

  /// Load settings from SharedPreferences
  static Future<StorySettings> load() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final fontSize = prefs.getDouble('story_font_size') ?? 18.0;
      final subtitleTransparency = prefs.getDouble('story_subtitle_transparency') ?? 50.0;
      final narrationSpeed = prefs.getDouble('story_narration_speed') ?? 1.0;
      final isAutoplayEnabled = prefs.getBool('story_autoplay_enabled') ?? false; // Rule 16: Default mode is manual

      AppLogger.debug('[STORY_SETTINGS] Settings loaded: fontSize=$fontSize, transparency=$subtitleTransparency, speed=$narrationSpeed, autoplay=$isAutoplayEnabled');

      return StorySettings(
        fontSize: fontSize,
        subtitleTransparency: subtitleTransparency,
        narrationSpeed: narrationSpeed,
        isAutoplayEnabled: isAutoplayEnabled,
      );
    } catch (e) {
      AppLogger.debug('[STORY_SETTINGS] Error loading settings, using defaults: $e');
      return const StorySettings();
    }
  }
}

/// Story settings overlay widget
class StorySettingsWidget extends StatefulWidget {
  final StorySettings initialSettings;
  final Function(StorySettings) onSettingsChanged;
  final VoidCallback onClose;

  const StorySettingsWidget({
    super.key,
    required this.initialSettings,
    required this.onSettingsChanged,
    required this.onClose,
  });

  @override
  State<StorySettingsWidget> createState() => _StorySettingsWidgetState();
}

class _StorySettingsWidgetState extends State<StorySettingsWidget>
    with SingleTickerProviderStateMixin {
  
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  late StorySettings _currentSettings;

  @override
  void initState() {
    super.initState();
    _currentSettings = widget.initialSettings;
    _initializeAnimations();
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// Initialize animations
  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
  }

  /// Update settings and notify parent
  void _updateSettings(StorySettings newSettings) {
    setState(() {
      _currentSettings = newSettings;
    });
    widget.onSettingsChanged(newSettings);
    newSettings.save(); // Persist settings
  }

  /// Close settings with animation
  Future<void> _closeSettings() async {
    await _animationController.reverse();
    widget.onClose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        color: Colors.black.withValues(alpha: 0.8),
        child: Center(
          child: SlideTransition(
            position: _slideAnimation,
            child: _buildSettingsPanel(),
          ),
        ),
      ),
    );
  }

  /// Build settings panel
  Widget _buildSettingsPanel() {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    return Container(
      width: isSmallScreen ? screenSize.width * 0.9 : 400,
      margin: EdgeInsets.all(isSmallScreen ? 16 : 32),
      padding: EdgeInsets.all(isSmallScreen ? 16 : 24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.9,
        ),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              _buildHeader(),

              const SizedBox(height: 24),

              // Settings controls
              _buildFontSizeControl(),
              const SizedBox(height: 20),
              _buildTransparencyControl(),
              const SizedBox(height: 20),
              _buildNarrationSpeedControl(),
              const SizedBox(height: 20),
              _buildAutoplayControl(),

              const SizedBox(height: 32),

              // Action buttons
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  /// Build header
  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        const Text(
          'Story Settings',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.deepPurple,
          ),
        ),
        IconButton(
          onPressed: _closeSettings,
          icon: const Icon(Icons.close),
          color: Colors.grey[600],
        ),
      ],
    );
  }

  /// Build font size control
  Widget _buildFontSizeControl() {
    return _buildSliderControl(
      title: 'Font Size',
      value: _currentSettings.fontSize,
      min: 12.0,
      max: 32.0,
      divisions: 20,
      unit: 'px',
      onChanged: (value) {
        _updateSettings(_currentSettings.copyWith(fontSize: value));
      },
      preview: Text(
        'Sample text',
        style: TextStyle(
          fontSize: _currentSettings.fontSize,
          color: Colors.grey[800],
        ),
      ),
    );
  }

  /// Build transparency control
  Widget _buildTransparencyControl() {
    return _buildSliderControl(
      title: 'Subtitle Background',
      value: _currentSettings.subtitleTransparency,
      min: 0.0,
      max: 100.0,
      divisions: 20,
      unit: '%',
      onChanged: (value) {
        _updateSettings(_currentSettings.copyWith(subtitleTransparency: value));
      },
      preview: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: _currentSettings.subtitleTransparency / 100),
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Text(
          'Subtitle preview',
          style: TextStyle(color: Colors.white),
        ),
      ),
    );
  }

  /// Build narration speed control
  Widget _buildNarrationSpeedControl() {
    return _buildSliderControl(
      title: 'Narration Speed',
      value: _currentSettings.narrationSpeed,
      min: 0.3,
      max: 2.0,
      divisions: 17,
      unit: 'x',
      onChanged: (value) {
        _updateSettings(_currentSettings.copyWith(narrationSpeed: value));
      },
      preview: Row(
        children: [
          Icon(
            _currentSettings.narrationSpeed < 0.8
                ? Icons.slow_motion_video
                : _currentSettings.narrationSpeed > 1.2
                    ? Icons.fast_forward
                    : Icons.play_arrow,
            color: Colors.grey[600],
          ),
          const SizedBox(width: 8),
          Text(
            '${_currentSettings.narrationSpeed.toStringAsFixed(1)}x speed',
            style: TextStyle(color: Colors.grey[800]),
          ),
        ],
      ),
    );
  }

  /// Build autoplay control
  Widget _buildAutoplayControl() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Story Progression',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.deepPurple,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _currentSettings.isAutoplayEnabled ? 'Autoplay Mode' : 'Manual Mode',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _currentSettings.isAutoplayEnabled
                          ? 'Stories advance automatically after narration'
                          : 'Tap to advance to the next scene',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Switch(
                value: _currentSettings.isAutoplayEnabled,
                onChanged: (value) {
                  _updateSettings(_currentSettings.copyWith(isAutoplayEnabled: value));
                },
                activeColor: Colors.deepPurple,
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        Center(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                _currentSettings.isAutoplayEnabled ? Icons.play_circle : Icons.touch_app,
                size: 20,
                color: Colors.grey[600],
              ),
              const SizedBox(width: 8),
              Text(
                _currentSettings.isAutoplayEnabled ? 'Automatic progression' : 'Manual progression',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Build generic slider control
  Widget _buildSliderControl({
    required String title,
    required double value,
    required double min,
    required double max,
    required int divisions,
    required String unit,
    required ValueChanged<double> onChanged,
    Widget? preview,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.deepPurple,
              ),
            ),
            Text(
              '${value.toStringAsFixed(value % 1 == 0 ? 0 : 1)}$unit',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Slider(
          value: value,
          min: min,
          max: max,
          divisions: divisions,
          onChanged: onChanged,
          activeColor: Colors.deepPurple,
          inactiveColor: Colors.grey[300],
        ),
        if (preview != null) ...[
          const SizedBox(height: 8),
          Center(child: preview),
        ],
      ],
    );
  }

  /// Build action buttons
  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Reset button
        TextButton(
          onPressed: () {
            _updateSettings(const StorySettings());
          },
          child: const Text(
            'Reset to Default',
            style: TextStyle(color: Colors.grey),
          ),
        ),
        
        // Done button
        ElevatedButton(
          onPressed: _closeSettings,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.deepPurple,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
          child: const Text('Done'),
        ),
      ],
    );
  }
}
